envoyProxyForGatewayClass:
  apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyProxy
  metadata:
    namespace: envoy-gateway-system
    name: test
  spec:
    disableLuaValidation: true
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: All
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-1
  spec:
    hostnames:
    - www.example.com
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/foo"
      backendRefs:
      - name: service-1
        port: 8080
envoyextensionpolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    namespace: default
    name: policy-for-http-route   # Invalid Lua but still gets accepted
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
    lua:
    - type: Inline
      inline: "function envoy_on_response(response_handle)
    response_handle:UnknownApi()
    end"
