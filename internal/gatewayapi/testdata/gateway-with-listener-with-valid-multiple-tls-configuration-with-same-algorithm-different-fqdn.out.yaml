gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: tls
      port: 443
      protocol: HTTPS
      tls:
        certificateRefs:
        - group: null
          kind: null
          name: tls-secret-ecdsa-1
        - group: null
          kind: null
          name: tls-secret-ecdsa-2
        mode: Terminate
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: tls
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: default
  spec:
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/tls
        ports:
        - containerPort: 10443
          name: https-443
          protocol: HTTPS
          servicePort: 443
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: tls
      name: envoy-gateway/gateway-1/tls
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10443
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: '*'
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/*
        pathMatch:
          distinct: false
          name: ""
          prefix: /
      tls:
        alpnProtocols: null
        certificates:
        - certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJnVENDQVNlZ0F3SUJBZ0lVRm1sOExCRzBvL1FLNFErWjdrODI0c0MyaUZ3d0NnWUlLb1pJemowRUF3SXcKRmpFVU1CSUdBMVVFQXd3TFptOXZMbUpoY2k1amIyMHdIaGNOTWpRd01qSTVNRGt6TURFd1doY05NelF3TWpJMgpNRGt6TURFd1dqQVdNUlF3RWdZRFZRUUREQXRtYjI4dVltRnlMbU52YlRCWk1CTUdCeXFHU000OUFnRUdDQ3FHClNNNDlBd0VIQTBJQUJMYVl2cUt1VlZveERvNTJlV3p2WUI1anc3RU1GODZybXlvaTVadWF5emRNdnBnNHpCcjgKUktCak5zK1QxakI4T0t1Y1MvN1JVRHgwcHorOTc2ek0zaU9qVXpCUk1CMEdBMVVkRGdRV0JCVE82K2NnMFIwZAp3dHJ6SlFQRzZnNzZoQkJVelRBZkJnTlZIU01FR0RBV2dCVE82K2NnMFIwZHd0cnpKUVBHNmc3NmhCQlV6VEFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUFvR0NDcUdTTTQ5QkFNQ0EwZ0FNRVVDSVFDMlhwUFFnUXpXYWUzYjVwWnQKR2N1TWZESjBjME9QS2NuZWdrWFoyQzRCM2dJZ1Uvc1Jrd0lwTFFOUlYrRWFZdzRQNVQ1Z1BFNlkrVnBtQzk4aApvVmpaL3pRPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0t
          name: envoy-gateway/tls-secret-ecdsa-1
          privateKey: '[redacted]'
        - certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJ5RENDQVU2Z0F3SUJBZ0lVUWltVUFlSExNdHo0dEdSdG5oNG9qWHRhVXpzd0NnWUlLb1pJemowRUF3SXcKR3pFWk1CY0dBMVVFQXd3UWRHVnpkQzVsZUdGdGNHeGxMbU52YlRBZUZ3MHlOREExTWpVd09URXhNemRhRncwegpOREExTWpNd09URXhNemRhTUJzeEdUQVhCZ05WQkFNTUVIUmxjM1F1WlhoaGJYQnNaUzVqYjIwd2RqQVFCZ2NxCmhrak9QUUlCQmdVcmdRUUFJZ05pQUFSNHkwY0xlRWg2clpDeDJTMUtMOWsxSDh3bzFxOUtiY2MyZnUwYWlCK3AKcXFmd0JLQWNocnZKUlQ3NCt5Z01QcVIvNzRKN3U2eDNTWkE3VktkMWdoYVBaQXVJalBRMWtmd0g5Q3ZpTHNuUQpnckN4Q1ZTZTZkbXEva3BqMU10QnJTZWpVekJSTUIwR0ExVWREZ1FXQkJUYVNlb1RtY3JlRU5Kd0t5ZmlZS3JnCjlIdnFVREFmQmdOVkhTTUVHREFXZ0JUYVNlb1RtY3JlRU5Kd0t5ZmlZS3JnOUh2cVVEQVBCZ05WSFJNQkFmOEUKQlRBREFRSC9NQW9HQ0NxR1NNNDlCQU1DQTJnQU1HVUNNRzFPSlUrRTlEaCt4TjdJMFZVTXIwdmt3S0h6V2Q3NwpTQXFXQjJVcG4vNThQTzd3eWNvWHZNMjlwREU0SkUvRzVRSXhBT2FhemxKZ1M3Z081eU50aW1tZ0llWFJ1K2pwCkNXb3kxb3hZU2ZSMmh1YkJ1Q1RUUkFqNkhPODBjTUVrZHFrMWp3PT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          name: envoy-gateway/tls-secret-ecdsa-2
          privateKey: '[redacted]'
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
