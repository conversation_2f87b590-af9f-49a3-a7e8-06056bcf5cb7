gateways:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    metadata:
      namespace: envoy-gateway
      name: gateway-1
    spec:
      gatewayClassName: envoy-gateway-class
      listeners:
        - name: http
          protocol: HTTP
          port: 80
          allowedRoutes:
            namespaces:
              from: All
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    metadata:
      namespace: envoy-gateway
      name: gateway-2
    spec:
      gatewayClassName: envoy-gateway-class
      listeners:
        - name: http
          protocol: HTTP
          port: 80
          allowedRoutes:
            namespaces:
              from: All
httpRoutes:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: HTTPRoute
    metadata:
      namespace: default
      name: httproute-1
    spec:
      hostnames:
        - gateway.envoyproxy.io
      parentRefs:
        - namespace: envoy-gateway
          name: gateway-1
          sectionName: http
        - namespace: envoy-gateway
          name: gateway-2
          sectionName: http
      rules:
        - matches:
            - path:
                value: "/"
          backendRefs:
            - name: service-1
              port: 8080
backendTrafficPolicies:
  - apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: BackendTrafficPolicy
    metadata:
      namespace: envoy-gateway
      name: policy-for-gateway1
    spec:
      targetRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
      timeout:
        tcp:
          connectTimeout: 15s
        http:
          connectionIdleTimeout: 16s
          maxConnectionDuration: 17s
      httpUpgrade:
        - type: websocket
  - apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: BackendTrafficPolicy
    metadata:
      namespace: default
      name: policy-for-route
    spec:
      targetRef:
        group: gateway.networking.k8s.io
        kind: HTTPRoute
        name: httproute-1
      mergeType: StrategicMerge
      timeout:
        tcp:
          connectTimeout: 10s
      connection:
        bufferLimit: 100M
      httpUpgrade:
        - type: "spdy/3.1"
      rateLimit:
        type: Global
        global:
          rules:
            - clientSelectors:
                - sourceCIDR:
                    type: Distinct
                    value: 0.0.0.0/0
              limit:
                requests: 5
                unit: Hour
              shared: true
            - clientSelectors:
                - headers:
                    - name: x-user-id
                      type: Exact
                      value: one
              limit:
                requests: 5
                unit: Hour
              shared: true
