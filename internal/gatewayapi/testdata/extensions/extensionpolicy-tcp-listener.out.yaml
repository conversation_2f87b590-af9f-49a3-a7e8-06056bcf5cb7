extensionServerPolicies:
- apiVersion: foo.example.io/v1alpha1
  kind: Bar
  metadata:
    name: test1
    namespace: envoy-gateway
  spec:
    data: attached to all listeners
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: foo.example.io/v1alpha1
  kind: Bar
  metadata:
    name: test2
    namespace: envoy-gateway
  spec:
    data: attached only to listener on port 80
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
      sectionName: tcp1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: tcp1
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: tcp1
      port: 80
      protocol: TCP
    - allowedRoutes:
        namespaces:
          from: All
      name: tcp2
      port: 81
      protocol: TCP
  status:
    listeners:
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: tcp1
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: TCPRoute
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: tcp2
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: TCPRoute
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/tcp1
        ports:
        - containerPort: 10080
          name: tcp-80
          protocol: TCP
          servicePort: 80
      - address: null
        name: envoy-gateway/gateway-1/tcp2
        ports:
        - containerPort: 10081
          name: tcp-81
          protocol: TCP
          servicePort: 81
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-1
      namespace: ""
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    extensionServerPolicies:
    - object:
        apiVersion: foo.example.io/v1alpha1
        kind: Bar
        metadata:
          name: test1
          namespace: envoy-gateway
        spec:
          data: attached to all listeners
          targetRef:
            group: gateway.networking.k8s.io
            kind: Gateway
            name: gateway-1
    - object:
        apiVersion: foo.example.io/v1alpha1
        kind: Bar
        metadata:
          name: test2
          namespace: envoy-gateway
        spec:
          data: attached only to listener on port 80
          targetRef:
            group: gateway.networking.k8s.io
            kind: Gateway
            name: gateway-1
            sectionName: tcp1
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
    tcp:
    - address: 0.0.0.0
      extensionRefs:
      - object:
          apiVersion: foo.example.io/v1alpha1
          kind: Bar
          metadata:
            name: test1
            namespace: envoy-gateway
          spec:
            data: attached to all listeners
            targetRef:
              group: gateway.networking.k8s.io
              kind: Gateway
              name: gateway-1
          status:
            ancestors:
            - ancestorRef:
                group: gateway.networking.k8s.io
                kind: Gateway
                name: gateway-1
                namespace: envoy-gateway
              conditions:
              - lastTransitionTime: null
                message: Policy has been accepted.
                reason: Accepted
                status: "True"
                type: Accepted
              controllerName: gateway.envoyproxy.io/gatewayclass-controller
      - object:
          apiVersion: foo.example.io/v1alpha1
          kind: Bar
          metadata:
            name: test2
            namespace: envoy-gateway
          spec:
            data: attached only to listener on port 80
            targetRef:
              group: gateway.networking.k8s.io
              kind: Gateway
              name: gateway-1
              sectionName: tcp1
          status:
            ancestors:
            - ancestorRef:
                group: gateway.networking.k8s.io
                kind: Gateway
                name: gateway-1
                namespace: envoy-gateway
                sectionName: tcp1
              conditions:
              - lastTransitionTime: null
                message: Policy has been accepted.
                reason: Accepted
                status: "True"
                type: Accepted
              controllerName: gateway.envoyproxy.io/gatewayclass-controller
      name: envoy-gateway/gateway-1/tcp1
      port: 10080
    - address: 0.0.0.0
      extensionRefs:
      - object:
          apiVersion: foo.example.io/v1alpha1
          kind: Bar
          metadata:
            name: test1
            namespace: envoy-gateway
          spec:
            data: attached to all listeners
            targetRef:
              group: gateway.networking.k8s.io
              kind: Gateway
              name: gateway-1
          status:
            ancestors:
            - ancestorRef:
                group: gateway.networking.k8s.io
                kind: Gateway
                name: gateway-1
                namespace: envoy-gateway
              conditions:
              - lastTransitionTime: null
                message: Policy has been accepted.
                reason: Accepted
                status: "True"
                type: Accepted
              controllerName: gateway.envoyproxy.io/gatewayclass-controller
      name: envoy-gateway/gateway-1/tcp2
      port: 10081
