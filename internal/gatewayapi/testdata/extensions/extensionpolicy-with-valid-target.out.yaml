extensionServerPolicies:
- apiVersion: foo.example.io/v1alpha1
  kind: Bar
  metadata:
    name: test1
    namespace: envoy-gateway
  spec:
    data: attached to all listeners
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: foo.example.io/v1alpha1
  kind: Bar
  metadata:
    name: test2
    namespace: envoy-gateway
  spec:
    data: attached only to listener on port 80
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
      sectionName: http2
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http2
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http1
      port: 81
      protocol: HTTP
    - allowedRoutes:
        namespaces:
          from: All
      name: http2
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http1
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http2
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http1
        ports:
        - containerPort: 10081
          name: http-81
          protocol: HTTP
          servicePort: 81
      - address: null
        name: envoy-gateway/gateway-1/http2
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-1
      namespace: ""
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    extensionServerPolicies:
    - object:
        apiVersion: foo.example.io/v1alpha1
        kind: Bar
        metadata:
          name: test1
          namespace: envoy-gateway
        spec:
          data: attached to all listeners
          targetRef:
            group: gateway.networking.k8s.io
            kind: Gateway
            name: gateway-1
    - object:
        apiVersion: foo.example.io/v1alpha1
        kind: Bar
        metadata:
          name: test2
          namespace: envoy-gateway
        spec:
          data: attached only to listener on port 80
          targetRef:
            group: gateway.networking.k8s.io
            kind: Gateway
            name: gateway-1
            sectionName: http2
    http:
    - address: 0.0.0.0
      extensionRefs:
      - object:
          apiVersion: foo.example.io/v1alpha1
          kind: Bar
          metadata:
            name: test1
            namespace: envoy-gateway
          spec:
            data: attached to all listeners
            targetRef:
              group: gateway.networking.k8s.io
              kind: Gateway
              name: gateway-1
          status:
            ancestors:
            - ancestorRef:
                group: gateway.networking.k8s.io
                kind: Gateway
                name: gateway-1
                namespace: envoy-gateway
              conditions:
              - lastTransitionTime: null
                message: Policy has been accepted.
                reason: Accepted
                status: "True"
                type: Accepted
              controllerName: gateway.envoyproxy.io/gatewayclass-controller
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http1
      name: envoy-gateway/gateway-1/http1
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10081
    - address: 0.0.0.0
      extensionRefs:
      - object:
          apiVersion: foo.example.io/v1alpha1
          kind: Bar
          metadata:
            name: test1
            namespace: envoy-gateway
          spec:
            data: attached to all listeners
            targetRef:
              group: gateway.networking.k8s.io
              kind: Gateway
              name: gateway-1
          status:
            ancestors:
            - ancestorRef:
                group: gateway.networking.k8s.io
                kind: Gateway
                name: gateway-1
                namespace: envoy-gateway
              conditions:
              - lastTransitionTime: null
                message: Policy has been accepted.
                reason: Accepted
                status: "True"
                type: Accepted
              controllerName: gateway.envoyproxy.io/gatewayclass-controller
      - object:
          apiVersion: foo.example.io/v1alpha1
          kind: Bar
          metadata:
            name: test2
            namespace: envoy-gateway
          spec:
            data: attached only to listener on port 80
            targetRef:
              group: gateway.networking.k8s.io
              kind: Gateway
              name: gateway-1
              sectionName: http2
          status:
            ancestors:
            - ancestorRef:
                group: gateway.networking.k8s.io
                kind: Gateway
                name: gateway-1
                namespace: envoy-gateway
                sectionName: http2
              conditions:
              - lastTransitionTime: null
                message: Policy has been accepted.
                reason: Accepted
                status: "True"
                type: Accepted
              controllerName: gateway.envoyproxy.io/gatewayclass-controller
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http2
      name: envoy-gateway/gateway-1/http2
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
