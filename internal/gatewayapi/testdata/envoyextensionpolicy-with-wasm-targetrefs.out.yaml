envoyExtensionPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: EnvoyExtensionPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-gateway
    namespace: envoy-gateway
  spec:
    targetRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
    wasm:
    - code:
        http:
          sha256: 2d89c4c6ab2a1c615c7696ed37ade9e50654ac70384b5d45100eb08e62130ff4
          url: https://www.example.com/wasm-filter-1.wasm
        type: HTTP
      config:
        parameter1:
          key1: value1
          key2: value2
        parameter2: value3
      name: wasm-filter-1
    - code:
        http:
          sha256: 84274ca23246855cc491b3c6a657a89167e0b109a7ae380f1e64df77c910307e
          url: https://www.example.com/wasm-filter-2.wasm
        type: HTTP
      config:
        parameter1: value1
        parameter2: value2
      name: wasm-filter-2
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: 'This policy is being overridden by other envoyExtensionPolicies
          for these routes: [envoy-gateway/httproute-1]'
        reason: Overridden
        status: "True"
        type: Overridden
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 2
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: envoy-gateway
  spec:
    hostnames:
    - www.example.com
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /foo
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-2
    namespace: envoy-gateway
  spec:
    hostnames:
    - www.example.com
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /bar
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    globalResources:
      envoyClientCertificate:
        certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUREVENDQWZXZ0F3SUJBZ0lVRUZNaFA5ZUo5WEFCV3NRNVptNmJSazJjTE5Rd0RRWUpLb1pJaHZjTkFRRUwKQlFBd0ZqRVVNQklHQTFVRUF3d0xabTl2TG1KaGNpNWpiMjB3SGhjTk1qUXdNakk1TURrek1ERXdXaGNOTXpRdwpNakkyTURrek1ERXdXakFXTVJRd0VnWURWUVFEREF0bWIyOHVZbUZ5TG1OdmJUQ0NBU0l3RFFZSktvWklodmNOCkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFKbEk2WXhFOVprQ1BzNnBDUXhickNtZWl4OVA1RGZ4OVJ1NUxENFQKSm1kVzdJS2R0UVYvd2ZMbXRzdTc2QithVGRDaldlMEJUZmVPT1JCYlIzY1BBRzZFbFFMaWNsUVVydW4zcStncwpKcEsrSTdjSStqNXc4STY4WEg1V1E3clZVdGJ3SHBxYncrY1ZuQnFJVU9MaUlhdGpJZjdLWDUxTTF1RjljZkVICkU0RG5jSDZyYnI1OS9SRlpCc2toeHM1T3p3Sklmb2hreXZGd2V1VHd4Sy9WcGpJKzdPYzQ4QUJDWHBOTzlEL3EKRWgrck9hdWpBTWNYZ0hRSVRrQ2lpVVRjVW82TFNIOXZMWlB0YXFmem9acTZuaE1xcFc2NUUxcEF3RjNqeVRUeAphNUk4SmNmU0Zqa2llWjIwTFVRTW43TThVNHhIamFvL2d2SDBDQWZkQjdSTFUyc0NBd0VBQWFOVE1GRXdIUVlEClZSME9CQllFRk9SQ0U4dS8xRERXN2loWnA3Y3g5dFNtUG02T01COEdBMVVkSXdRWU1CYUFGT1JDRTh1LzFERFcKN2loWnA3Y3g5dFNtUG02T01BOEdBMVVkRXdFQi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQgpBRnQ1M3pqc3FUYUg1YThFMmNodm1XQWdDcnhSSzhiVkxNeGl3TkdqYm1FUFJ6K3c2TngrazBBOEtFY0lEc0tjClNYY2k1OHU0b1didFZKQmx6YS9adWpIUjZQMUJuT3BsK2FveTc4NGJiZDRQMzl3VExvWGZNZmJCQ20xdmV2aDkKQUpLbncyWnRxcjRta2JMY3hFcWxxM3NCTEZBUzlzUUxuS05DZTJjR0xkVHAyYm9HK3FjZ3lRZ0NJTTZmOEVNdgpXUGlmQ01NR3V6Sy9HUkY0YlBPL1lGNDhld0R1M1VlaWgwWFhkVUFPRTlDdFVhOE5JaGMxVVBhT3pQcnRZVnFyClpPR2t2L0t1K0I3OGg4U0VzTzlYclFjdXdiT25KeDZLdFIrYWV5a3ZBcFhDUTNmWkMvYllLQUFSK1A4QUpvUVoKYndJVW1YaTRnajVtK2JLUGhlK2lyK0U9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0=
        name: envoy-gateway-system/envoy
        privateKey: '[redacted]'
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: envoy-gateway
          name: httproute/envoy-gateway/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              kind: Service
              name: service-1
              namespace: envoy-gateway
              sectionName: "8080"
            name: httproute/envoy-gateway/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
        envoyExtensions:
          wasms:
          - config:
              parameter1:
                key1: value1
                key2: value2
              parameter2: value3
            failOpen: false
            httpWasmCode:
              originalDownloadingURL: https://www.example.com/wasm-filter-1.wasm
              servingURL: https://envoy-gateway.envoy-gateway-system.svc.cluster.local:18002/5c90b9a82642ce00a7753923fabead306b9d9a54a7c0bd2463a1af3efcfb110b.wasm
              sha256: 2d89c4c6ab2a1c615c7696ed37ade9e50654ac70384b5d45100eb08e62130ff4
            name: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/0
            wasmName: wasm-filter-1
          - config:
              parameter1: value1
              parameter2: value2
            failOpen: false
            httpWasmCode:
              originalDownloadingURL: https://www.example.com/wasm-filter-2.wasm
              servingURL: https://envoy-gateway.envoy-gateway-system.svc.cluster.local:18002/593e4cc60a7e0fa4d4f86531a5e20e785213a52000f056a7a8b5c5afcb908052.wasm
              sha256: 84274ca23246855cc491b3c6a657a89167e0b109a7ae380f1e64df77c910307e
            name: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/1
            wasmName: wasm-filter-2
        hostname: www.example.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: envoy-gateway
        name: httproute/envoy-gateway/httproute-1/rule/0/match/0/www_example_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /foo
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-2
            namespace: envoy-gateway
          name: httproute/envoy-gateway/httproute-2/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              kind: Service
              name: service-1
              namespace: envoy-gateway
              sectionName: "8080"
            name: httproute/envoy-gateway/httproute-2/rule/0/backend/0
            protocol: HTTP
            weight: 1
        envoyExtensions:
          wasms:
          - config:
              parameter1:
                key1: value1
                key2: value2
              parameter2: value3
            failOpen: false
            httpWasmCode:
              originalDownloadingURL: https://www.example.com/wasm-filter-1.wasm
              servingURL: https://envoy-gateway.envoy-gateway-system.svc.cluster.local:18002/5c90b9a82642ce00a7753923fabead306b9d9a54a7c0bd2463a1af3efcfb110b.wasm
              sha256: 2d89c4c6ab2a1c615c7696ed37ade9e50654ac70384b5d45100eb08e62130ff4
            name: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/0
            wasmName: wasm-filter-1
          - config:
              parameter1: value1
              parameter2: value2
            failOpen: false
            httpWasmCode:
              originalDownloadingURL: https://www.example.com/wasm-filter-2.wasm
              servingURL: https://envoy-gateway.envoy-gateway-system.svc.cluster.local:18002/593e4cc60a7e0fa4d4f86531a5e20e785213a52000f056a7a8b5c5afcb908052.wasm
              sha256: 84274ca23246855cc491b3c6a657a89167e0b109a7ae380f1e64df77c910307e
            name: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/1
            wasmName: wasm-filter-2
        hostname: www.example.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-2
          namespace: envoy-gateway
        name: httproute/envoy-gateway/httproute-2/rule/0/match/0/www_example_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /bar
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
