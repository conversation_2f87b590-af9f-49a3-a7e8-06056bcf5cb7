clientTrafficPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: ClientTrafficPolicy
  metadata:
    creationTimestamp: null
    name: target-gateway-1
    namespace: envoy-gateway
  spec:
    headers:
      xForwardedClientCert:
        mode: Sanitize
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
    tls:
      clientValidation:
        caCertificateRefs:
        - group: null
          kind: null
          name: tls-secret-1
          namespace: envoy-gateway
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: ClientTrafficPolicy
  metadata:
    creationTimestamp: null
    name: target-gateway-2
    namespace: envoy-gateway
  spec:
    headers:
      xForwardedClientCert:
        certDetailsToAdd:
        - URI
        - DNS
        mode: ForwardOnly
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-2
    tls:
      clientValidation:
        caCertificateRefs:
        - group: null
          kind: ConfigMap
          name: ca-configmap
          namespace: envoy-gateway
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-2
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: ClientTrafficPolicy
  metadata:
    creationTimestamp: null
    name: target-gateway-3
    namespace: envoy-gateway
  spec:
    headers:
      xForwardedClientCert:
        mode: AppendForward
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-3
    tls:
      clientValidation:
        caCertificateRefs:
        - group: null
          kind: ConfigMap
          name: ca-configmap
          namespace: envoy-gateway
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-3
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: ClientTrafficPolicy
  metadata:
    creationTimestamp: null
    name: target-gateway-4
    namespace: envoy-gateway
  spec:
    headers:
      xForwardedClientCert:
        mode: SanitizeSet
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-4
    tls:
      clientValidation:
        caCertificateRefs:
        - group: null
          kind: ConfigMap
          name: ca-configmap
          namespace: envoy-gateway
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-4
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: ClientTrafficPolicy
  metadata:
    creationTimestamp: null
    name: target-gateway-5
    namespace: envoy-gateway
  spec:
    headers:
      xForwardedClientCert:
        mode: AlwaysForwardOnly
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-5
    tls:
      clientValidation:
        caCertificateRefs:
        - group: null
          kind: ConfigMap
          name: ca-configmap
          namespace: envoy-gateway
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-5
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: http-1
      port: 443
      protocol: HTTPS
      tls:
        certificateRefs:
        - group: null
          kind: null
          name: tls-secret-1
          namespace: envoy-gateway
        mode: Terminate
    - allowedRoutes:
        namespaces:
          from: Same
      name: http-2
      port: 8080
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http-1
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http-2
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-2
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: http-1
      port: 443
      protocol: HTTPS
      tls:
        certificateRefs:
        - group: null
          kind: null
          name: tls-secret-1
          namespace: envoy-gateway
        mode: Terminate
  status:
    listeners:
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http-1
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-3
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: http-1
      port: 443
      protocol: HTTPS
      tls:
        certificateRefs:
        - group: null
          kind: null
          name: tls-secret-1
          namespace: envoy-gateway
        mode: Terminate
  status:
    listeners:
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http-1
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-4
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: http-1
      port: 443
      protocol: HTTPS
      tls:
        certificateRefs:
        - group: null
          kind: null
          name: tls-secret-1
          namespace: envoy-gateway
        mode: Terminate
  status:
    listeners:
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http-1
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-5
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: http-1
      port: 443
      protocol: HTTPS
      tls:
        certificateRefs:
        - group: null
          kind: null
          name: tls-secret-1
          namespace: envoy-gateway
        mode: Terminate
  status:
    listeners:
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http-1
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http-1
        ports:
        - containerPort: 10443
          name: https-443
          protocol: HTTPS
          servicePort: 443
      - address: null
        name: envoy-gateway/gateway-1/http-2
        ports:
        - containerPort: 8080
          name: http-8080
          protocol: HTTP
          servicePort: 8080
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
  envoy-gateway/gateway-2:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-2/http-1
        ports:
        - containerPort: 10443
          name: https-443
          protocol: HTTPS
          servicePort: 443
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-2
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-2
      namespace: envoy-gateway-system
  envoy-gateway/gateway-3:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-3/http-1
        ports:
        - containerPort: 10443
          name: https-443
          protocol: HTTPS
          servicePort: 443
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-3
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-3
      namespace: envoy-gateway-system
  envoy-gateway/gateway-4:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-4/http-1
        ports:
        - containerPort: 10443
          name: https-443
          protocol: HTTPS
          servicePort: 443
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-4
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-4
      namespace: envoy-gateway-system
  envoy-gateway/gateway-5:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-5/http-1
        ports:
        - containerPort: 10443
          name: https-443
          protocol: HTTPS
          servicePort: 443
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-5
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-5
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      headers:
        withUnderscoresAction: RejectRequest
        xForwardedClientCert:
          mode: Sanitize
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http-1
      name: envoy-gateway/gateway-1/http-1
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10443
      tls:
        alpnProtocols: null
        caCertificate:
          certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUR6akNDQXJhZ0F3SUJBZ0lVT0dKOUx1VGtKWkU0NmNVaUpGYmJ2bm10elFvd0RRWUpLb1pJaHZjTkFRRUwKQlFBd2J6RUxNQWtHQTFVRUJoTUNWVk14Q3pBSkJnTlZCQWdNQWxaQk1SRXdEd1lEVlFRSERBaFRiMjFsUTJsMAplVEVUTUJFR0ExVUVDZ3dLUlc1MmIzbFFjbTk0ZVRFUU1BNEdBMVVFQ3d3SFIyRjBaWGRoZVRFWk1CY0dBMVVFCkF3d1FiWFJzY3k1bGVHRnRjR3hsTG1OdmJUQWdGdzB5TkRBM01UWXlNalV4TWpOYUdBOHlNVEkwTURZeU1qSXkKTlRFeU0xb3diekVMTUFrR0ExVUVCaE1DVlZNeEN6QUpCZ05WQkFnTUFsWkJNUkV3RHdZRFZRUUhEQWhUYjIxbApRMmwwZVRFVE1CRUdBMVVFQ2d3S1JXNTJiM2xRY205NGVURVFNQTRHQTFVRUN3d0hSMkYwWlhkaGVURVpNQmNHCkExVUVBd3dRYlhSc2N5NWxlR0Z0Y0d4bExtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0MKQVFvQ2dnRUJBS3kwZnp5NWFaVnRNajAxVWJPRGtsU1IxbTI1Mkt0QTJ2L2tmT05vNTZkNEJQbGdqVXdXUVZNUgpTclUxZzd4T2tWdjZiL0czdG5tQVhwWWY2VlIxODIyak96cCsxQ0c4ZWlGSEpjT2ZxV2lZMjh1NnFSV2VKUFZlCnpYdUFsMmd4cWJpTzZLdDZwbnI0aXFoVGhIK3ZqV3NXTnBDSVhrbDFydVlYbnhWLzRCOENxY1JJeTZHaEp6bloKRjR3NHJBNkRlRlJmZHl0MWd1bWtkN25PVVhYKzRZMzJrd0xGRU8zR3NnUTh1aVpEQmN1UEs5RjRHRDUydDZYTgowT2tlNTU0eEl2VldvZ1M1Vzl4UFIvcU5kQlpIQ1pid05jZzRRTVllbWZva0pkUXo4elVJcnZ6VUltM3ZvOUs3CnE4MmI1eTVFSm4yWEU5OVMycDZUZnJlSG1sUHpKNHNDQXdFQUFhTmdNRjR3Q3dZRFZSMFBCQVFEQWdTd01CTUcKQTFVZEpRUU1NQW9HQ0NzR0FRVUZCd01CTUJzR0ExVWRFUVFVTUJLQ0VHMTBiSE11WlhoaGJYQnNaUzVqYjIwdwpIUVlEVlIwT0JCWUVGRm1FTjBqRVFpckpYeGlLRHFlK2tTMVV3Q2gyTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCCkFRQ0NTVlluRVJPbHJpWDM2M0VtRzd1b091Nm54ajU1eWVmOXRKbnRubFVMVFZsMjlqc205Z3d5VnFUVCtUcXMKdzFPYW01TExmMEpjSWNRdmFUM203b0RpMElDUUo5eTlRQkNwMTh1TDBUeElDaFdVRTVnRUIxM3MyTzEwWWNFMQp1K2ozSzM0MStQNStoaEJpQnJ1d0dncStkVVRGRTZTYVVMY0xMVlB1RjdTeG1KbTRHK0Q0NVlqM1NDVy9aMzU2CkFXZzB4L0prZGFKSVVLVFJaUDVJTEZKQ1lJTUM3QWI1RmdWeGRCVW5kNWxheUZGb2NVMzRQaDlwZUxiYW00alYKdGt0SGNTSFJ6OERNTm1PNHpHTEZYNzlQR0lsaTZzTDU3V0N6bkw5RFFtajRyajFIS2tyeEdnMVExbUcwbDhOTQo5cXQyWEZNNUttWkVOb2E1MmFWSklHYWoKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          name: envoy-gateway/target-gateway-1/ca.crt
        certificates:
        - certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUR6akNDQXJhZ0F3SUJBZ0lVT0dKOUx1VGtKWkU0NmNVaUpGYmJ2bm10elFvd0RRWUpLb1pJaHZjTkFRRUwKQlFBd2J6RUxNQWtHQTFVRUJoTUNWVk14Q3pBSkJnTlZCQWdNQWxaQk1SRXdEd1lEVlFRSERBaFRiMjFsUTJsMAplVEVUTUJFR0ExVUVDZ3dLUlc1MmIzbFFjbTk0ZVRFUU1BNEdBMVVFQ3d3SFIyRjBaWGRoZVRFWk1CY0dBMVVFCkF3d1FiWFJzY3k1bGVHRnRjR3hsTG1OdmJUQWdGdzB5TkRBM01UWXlNalV4TWpOYUdBOHlNVEkwTURZeU1qSXkKTlRFeU0xb3diekVMTUFrR0ExVUVCaE1DVlZNeEN6QUpCZ05WQkFnTUFsWkJNUkV3RHdZRFZRUUhEQWhUYjIxbApRMmwwZVRFVE1CRUdBMVVFQ2d3S1JXNTJiM2xRY205NGVURVFNQTRHQTFVRUN3d0hSMkYwWlhkaGVURVpNQmNHCkExVUVBd3dRYlhSc2N5NWxlR0Z0Y0d4bExtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0MKQVFvQ2dnRUJBS3kwZnp5NWFaVnRNajAxVWJPRGtsU1IxbTI1Mkt0QTJ2L2tmT05vNTZkNEJQbGdqVXdXUVZNUgpTclUxZzd4T2tWdjZiL0czdG5tQVhwWWY2VlIxODIyak96cCsxQ0c4ZWlGSEpjT2ZxV2lZMjh1NnFSV2VKUFZlCnpYdUFsMmd4cWJpTzZLdDZwbnI0aXFoVGhIK3ZqV3NXTnBDSVhrbDFydVlYbnhWLzRCOENxY1JJeTZHaEp6bloKRjR3NHJBNkRlRlJmZHl0MWd1bWtkN25PVVhYKzRZMzJrd0xGRU8zR3NnUTh1aVpEQmN1UEs5RjRHRDUydDZYTgowT2tlNTU0eEl2VldvZ1M1Vzl4UFIvcU5kQlpIQ1pid05jZzRRTVllbWZva0pkUXo4elVJcnZ6VUltM3ZvOUs3CnE4MmI1eTVFSm4yWEU5OVMycDZUZnJlSG1sUHpKNHNDQXdFQUFhTmdNRjR3Q3dZRFZSMFBCQVFEQWdTd01CTUcKQTFVZEpRUU1NQW9HQ0NzR0FRVUZCd01CTUJzR0ExVWRFUVFVTUJLQ0VHMTBiSE11WlhoaGJYQnNaUzVqYjIwdwpIUVlEVlIwT0JCWUVGRm1FTjBqRVFpckpYeGlLRHFlK2tTMVV3Q2gyTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCCkFRQ0NTVlluRVJPbHJpWDM2M0VtRzd1b091Nm54ajU1eWVmOXRKbnRubFVMVFZsMjlqc205Z3d5VnFUVCtUcXMKdzFPYW01TExmMEpjSWNRdmFUM203b0RpMElDUUo5eTlRQkNwMTh1TDBUeElDaFdVRTVnRUIxM3MyTzEwWWNFMQp1K2ozSzM0MStQNStoaEJpQnJ1d0dncStkVVRGRTZTYVVMY0xMVlB1RjdTeG1KbTRHK0Q0NVlqM1NDVy9aMzU2CkFXZzB4L0prZGFKSVVLVFJaUDVJTEZKQ1lJTUM3QWI1RmdWeGRCVW5kNWxheUZGb2NVMzRQaDlwZUxiYW00alYKdGt0SGNTSFJ6OERNTm1PNHpHTEZYNzlQR0lsaTZzTDU3V0N6bkw5RFFtajRyajFIS2tyeEdnMVExbUcwbDhOTQo5cXQyWEZNNUttWkVOb2E1MmFWSklHYWoKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          name: envoy-gateway/tls-secret-1
          privateKey: '[redacted]'
        maxVersion: "1.3"
        minVersion: "1.2"
        requireClientCertificate: true
    - address: 0.0.0.0
      headers:
        withUnderscoresAction: RejectRequest
        xForwardedClientCert:
          mode: Sanitize
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http-2
      name: envoy-gateway/gateway-1/http-2
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 8080
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
  envoy-gateway/gateway-2:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      headers:
        withUnderscoresAction: RejectRequest
        xForwardedClientCert:
          mode: ForwardOnly
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-2
        namespace: envoy-gateway
        sectionName: http-1
      name: envoy-gateway/gateway-2/http-1
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10443
      tls:
        alpnProtocols: null
        caCertificate:
          certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRekNDQWl1Z0F3SUJBZ0lCQVRBTkJna3Foa2lHOXcwQkFRc0ZBREJDTVJNd0VRWURWUVFLRXdwRmJuWnYKZVZCeWIzaDVNUkF3RGdZRFZRUUxFd2RIWVhSbGQyRjVNUmt3RndZRFZRUURFeEJGYm5admVTQkhZWFJsZDJGNQpJRU5CTUNBWERUSTBNRE14TURFMU16SXhOMW9ZRHpJeE1qUXdNekV3TVRZek1qRTNXakJDTVJNd0VRWURWUVFLCkV3cEZiblp2ZVZCeWIzaDVNUkF3RGdZRFZRUUxFd2RIWVhSbGQyRjVNUmt3RndZRFZRUURFeEJGYm5admVTQkgKWVhSbGQyRjVJRU5CTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUE3WkZtR0I0ZQptMUtkR0VvaEFaQmZxeWRBRUdMREhKMVl5ZkhXZGQrdkJBZXZkVzY0Ylp4M3BnZ0pPdGdDbmVQdUZkMDJyRFFTCmRsc0psWC82bUZ0b1FpbG82d3Z4RFNKUmZhVERidGZUancrN2s4eWZkL0pzbWgwUldHK1VleUk3TmE5c1hBejcKYjU3bXB4c0NvTm93emVLNUVUaU9HR05XUGNqRU5Ka1NuQmFyejVtdU4wMHhJWldCVSt5TjVQTEpOeFp2eHBaSgpPbC9TU0k4c25vMGUwUHhBbXAzZmU3UWFYaVpqL1RBR0pQR3VUSmtVeHJIcXlaR0p0WVV4c1M4QTBkVDF6QmpqCml6QTVEcCtiNXl6WW8yM0hoN0JncGJaN1g0Z3NEVGhGdXdDRDZmSHllcHV2MnpIUHF2U3NkcWcyaEFoRHA5MVIKenJuN2E5R3hHMlZTSXdJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBUVl3RHdZRFZSMFRBUUgvQkFVdwpBd0VCL3pBZEJnTlZIUTRFRmdRVVVwUDFhWjFNMktJdVBQV3JOUERWMmM1Q25nb3dEUVlKS29aSWh2Y05BUUVMCkJRQURnZ0VCQUdTRWtBVnorWjBxUzRGbUEwcTRTQ3BJSXE2NGJzZEVqaVV6ZXY3cEsxTEVLMC9ZMjhRQlBpeFYKY1VYZmF4MThWUFI5cGxzMUpnWHRvOXFZK0MwaG5SWmljNjYxMVFUSmxXSzFwNmRpblEvZURkWUNCQytudjV4eApzc0FTd21wbEl4TXZqM1MxcUY2ZHI3c01JMlpWRDVIRWxUV2RPMTlVQkx5aGlLS1pXMkt4RHNZais1TlJ3R0ZlCkcrSnVEZ3E3bmpVTThtZHlZazBOZWhlZmRCVUVVVUNRdG53VXRXOTUvNDI5WHdxUVJPdVJEdGVHVDlrakQrWTUKZWE1bVc0bWZxTGV1R0pYWnM5YmRXaktLZExRUHJuOUlzaFB5c1dxejJIejhkUTFmN045L2c4VVdWU2pkNGN5eApTNUVBb2x6VnYweUI3d0hDV0NnZkcvY2tkT1RVTm5FPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          name: envoy-gateway/target-gateway-2/ca.crt
        certificates:
        - certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUR6akNDQXJhZ0F3SUJBZ0lVT0dKOUx1VGtKWkU0NmNVaUpGYmJ2bm10elFvd0RRWUpLb1pJaHZjTkFRRUwKQlFBd2J6RUxNQWtHQTFVRUJoTUNWVk14Q3pBSkJnTlZCQWdNQWxaQk1SRXdEd1lEVlFRSERBaFRiMjFsUTJsMAplVEVUTUJFR0ExVUVDZ3dLUlc1MmIzbFFjbTk0ZVRFUU1BNEdBMVVFQ3d3SFIyRjBaWGRoZVRFWk1CY0dBMVVFCkF3d1FiWFJzY3k1bGVHRnRjR3hsTG1OdmJUQWdGdzB5TkRBM01UWXlNalV4TWpOYUdBOHlNVEkwTURZeU1qSXkKTlRFeU0xb3diekVMTUFrR0ExVUVCaE1DVlZNeEN6QUpCZ05WQkFnTUFsWkJNUkV3RHdZRFZRUUhEQWhUYjIxbApRMmwwZVRFVE1CRUdBMVVFQ2d3S1JXNTJiM2xRY205NGVURVFNQTRHQTFVRUN3d0hSMkYwWlhkaGVURVpNQmNHCkExVUVBd3dRYlhSc2N5NWxlR0Z0Y0d4bExtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0MKQVFvQ2dnRUJBS3kwZnp5NWFaVnRNajAxVWJPRGtsU1IxbTI1Mkt0QTJ2L2tmT05vNTZkNEJQbGdqVXdXUVZNUgpTclUxZzd4T2tWdjZiL0czdG5tQVhwWWY2VlIxODIyak96cCsxQ0c4ZWlGSEpjT2ZxV2lZMjh1NnFSV2VKUFZlCnpYdUFsMmd4cWJpTzZLdDZwbnI0aXFoVGhIK3ZqV3NXTnBDSVhrbDFydVlYbnhWLzRCOENxY1JJeTZHaEp6bloKRjR3NHJBNkRlRlJmZHl0MWd1bWtkN25PVVhYKzRZMzJrd0xGRU8zR3NnUTh1aVpEQmN1UEs5RjRHRDUydDZYTgowT2tlNTU0eEl2VldvZ1M1Vzl4UFIvcU5kQlpIQ1pid05jZzRRTVllbWZva0pkUXo4elVJcnZ6VUltM3ZvOUs3CnE4MmI1eTVFSm4yWEU5OVMycDZUZnJlSG1sUHpKNHNDQXdFQUFhTmdNRjR3Q3dZRFZSMFBCQVFEQWdTd01CTUcKQTFVZEpRUU1NQW9HQ0NzR0FRVUZCd01CTUJzR0ExVWRFUVFVTUJLQ0VHMTBiSE11WlhoaGJYQnNaUzVqYjIwdwpIUVlEVlIwT0JCWUVGRm1FTjBqRVFpckpYeGlLRHFlK2tTMVV3Q2gyTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCCkFRQ0NTVlluRVJPbHJpWDM2M0VtRzd1b091Nm54ajU1eWVmOXRKbnRubFVMVFZsMjlqc205Z3d5VnFUVCtUcXMKdzFPYW01TExmMEpjSWNRdmFUM203b0RpMElDUUo5eTlRQkNwMTh1TDBUeElDaFdVRTVnRUIxM3MyTzEwWWNFMQp1K2ozSzM0MStQNStoaEJpQnJ1d0dncStkVVRGRTZTYVVMY0xMVlB1RjdTeG1KbTRHK0Q0NVlqM1NDVy9aMzU2CkFXZzB4L0prZGFKSVVLVFJaUDVJTEZKQ1lJTUM3QWI1RmdWeGRCVW5kNWxheUZGb2NVMzRQaDlwZUxiYW00alYKdGt0SGNTSFJ6OERNTm1PNHpHTEZYNzlQR0lsaTZzTDU3V0N6bkw5RFFtajRyajFIS2tyeEdnMVExbUcwbDhOTQo5cXQyWEZNNUttWkVOb2E1MmFWSklHYWoKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          name: envoy-gateway/tls-secret-1
          privateKey: '[redacted]'
        maxVersion: "1.3"
        minVersion: "1.2"
        requireClientCertificate: true
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
  envoy-gateway/gateway-3:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      headers:
        withUnderscoresAction: RejectRequest
        xForwardedClientCert:
          mode: AppendForward
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-3
        namespace: envoy-gateway
        sectionName: http-1
      name: envoy-gateway/gateway-3/http-1
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10443
      tls:
        alpnProtocols: null
        caCertificate:
          certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRekNDQWl1Z0F3SUJBZ0lCQVRBTkJna3Foa2lHOXcwQkFRc0ZBREJDTVJNd0VRWURWUVFLRXdwRmJuWnYKZVZCeWIzaDVNUkF3RGdZRFZRUUxFd2RIWVhSbGQyRjVNUmt3RndZRFZRUURFeEJGYm5admVTQkhZWFJsZDJGNQpJRU5CTUNBWERUSTBNRE14TURFMU16SXhOMW9ZRHpJeE1qUXdNekV3TVRZek1qRTNXakJDTVJNd0VRWURWUVFLCkV3cEZiblp2ZVZCeWIzaDVNUkF3RGdZRFZRUUxFd2RIWVhSbGQyRjVNUmt3RndZRFZRUURFeEJGYm5admVTQkgKWVhSbGQyRjVJRU5CTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUE3WkZtR0I0ZQptMUtkR0VvaEFaQmZxeWRBRUdMREhKMVl5ZkhXZGQrdkJBZXZkVzY0Ylp4M3BnZ0pPdGdDbmVQdUZkMDJyRFFTCmRsc0psWC82bUZ0b1FpbG82d3Z4RFNKUmZhVERidGZUancrN2s4eWZkL0pzbWgwUldHK1VleUk3TmE5c1hBejcKYjU3bXB4c0NvTm93emVLNUVUaU9HR05XUGNqRU5Ka1NuQmFyejVtdU4wMHhJWldCVSt5TjVQTEpOeFp2eHBaSgpPbC9TU0k4c25vMGUwUHhBbXAzZmU3UWFYaVpqL1RBR0pQR3VUSmtVeHJIcXlaR0p0WVV4c1M4QTBkVDF6QmpqCml6QTVEcCtiNXl6WW8yM0hoN0JncGJaN1g0Z3NEVGhGdXdDRDZmSHllcHV2MnpIUHF2U3NkcWcyaEFoRHA5MVIKenJuN2E5R3hHMlZTSXdJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBUVl3RHdZRFZSMFRBUUgvQkFVdwpBd0VCL3pBZEJnTlZIUTRFRmdRVVVwUDFhWjFNMktJdVBQV3JOUERWMmM1Q25nb3dEUVlKS29aSWh2Y05BUUVMCkJRQURnZ0VCQUdTRWtBVnorWjBxUzRGbUEwcTRTQ3BJSXE2NGJzZEVqaVV6ZXY3cEsxTEVLMC9ZMjhRQlBpeFYKY1VYZmF4MThWUFI5cGxzMUpnWHRvOXFZK0MwaG5SWmljNjYxMVFUSmxXSzFwNmRpblEvZURkWUNCQytudjV4eApzc0FTd21wbEl4TXZqM1MxcUY2ZHI3c01JMlpWRDVIRWxUV2RPMTlVQkx5aGlLS1pXMkt4RHNZais1TlJ3R0ZlCkcrSnVEZ3E3bmpVTThtZHlZazBOZWhlZmRCVUVVVUNRdG53VXRXOTUvNDI5WHdxUVJPdVJEdGVHVDlrakQrWTUKZWE1bVc0bWZxTGV1R0pYWnM5YmRXaktLZExRUHJuOUlzaFB5c1dxejJIejhkUTFmN045L2c4VVdWU2pkNGN5eApTNUVBb2x6VnYweUI3d0hDV0NnZkcvY2tkT1RVTm5FPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          name: envoy-gateway/target-gateway-3/ca.crt
        certificates:
        - certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUR6akNDQXJhZ0F3SUJBZ0lVT0dKOUx1VGtKWkU0NmNVaUpGYmJ2bm10elFvd0RRWUpLb1pJaHZjTkFRRUwKQlFBd2J6RUxNQWtHQTFVRUJoTUNWVk14Q3pBSkJnTlZCQWdNQWxaQk1SRXdEd1lEVlFRSERBaFRiMjFsUTJsMAplVEVUTUJFR0ExVUVDZ3dLUlc1MmIzbFFjbTk0ZVRFUU1BNEdBMVVFQ3d3SFIyRjBaWGRoZVRFWk1CY0dBMVVFCkF3d1FiWFJzY3k1bGVHRnRjR3hsTG1OdmJUQWdGdzB5TkRBM01UWXlNalV4TWpOYUdBOHlNVEkwTURZeU1qSXkKTlRFeU0xb3diekVMTUFrR0ExVUVCaE1DVlZNeEN6QUpCZ05WQkFnTUFsWkJNUkV3RHdZRFZRUUhEQWhUYjIxbApRMmwwZVRFVE1CRUdBMVVFQ2d3S1JXNTJiM2xRY205NGVURVFNQTRHQTFVRUN3d0hSMkYwWlhkaGVURVpNQmNHCkExVUVBd3dRYlhSc2N5NWxlR0Z0Y0d4bExtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0MKQVFvQ2dnRUJBS3kwZnp5NWFaVnRNajAxVWJPRGtsU1IxbTI1Mkt0QTJ2L2tmT05vNTZkNEJQbGdqVXdXUVZNUgpTclUxZzd4T2tWdjZiL0czdG5tQVhwWWY2VlIxODIyak96cCsxQ0c4ZWlGSEpjT2ZxV2lZMjh1NnFSV2VKUFZlCnpYdUFsMmd4cWJpTzZLdDZwbnI0aXFoVGhIK3ZqV3NXTnBDSVhrbDFydVlYbnhWLzRCOENxY1JJeTZHaEp6bloKRjR3NHJBNkRlRlJmZHl0MWd1bWtkN25PVVhYKzRZMzJrd0xGRU8zR3NnUTh1aVpEQmN1UEs5RjRHRDUydDZYTgowT2tlNTU0eEl2VldvZ1M1Vzl4UFIvcU5kQlpIQ1pid05jZzRRTVllbWZva0pkUXo4elVJcnZ6VUltM3ZvOUs3CnE4MmI1eTVFSm4yWEU5OVMycDZUZnJlSG1sUHpKNHNDQXdFQUFhTmdNRjR3Q3dZRFZSMFBCQVFEQWdTd01CTUcKQTFVZEpRUU1NQW9HQ0NzR0FRVUZCd01CTUJzR0ExVWRFUVFVTUJLQ0VHMTBiSE11WlhoaGJYQnNaUzVqYjIwdwpIUVlEVlIwT0JCWUVGRm1FTjBqRVFpckpYeGlLRHFlK2tTMVV3Q2gyTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCCkFRQ0NTVlluRVJPbHJpWDM2M0VtRzd1b091Nm54ajU1eWVmOXRKbnRubFVMVFZsMjlqc205Z3d5VnFUVCtUcXMKdzFPYW01TExmMEpjSWNRdmFUM203b0RpMElDUUo5eTlRQkNwMTh1TDBUeElDaFdVRTVnRUIxM3MyTzEwWWNFMQp1K2ozSzM0MStQNStoaEJpQnJ1d0dncStkVVRGRTZTYVVMY0xMVlB1RjdTeG1KbTRHK0Q0NVlqM1NDVy9aMzU2CkFXZzB4L0prZGFKSVVLVFJaUDVJTEZKQ1lJTUM3QWI1RmdWeGRCVW5kNWxheUZGb2NVMzRQaDlwZUxiYW00alYKdGt0SGNTSFJ6OERNTm1PNHpHTEZYNzlQR0lsaTZzTDU3V0N6bkw5RFFtajRyajFIS2tyeEdnMVExbUcwbDhOTQo5cXQyWEZNNUttWkVOb2E1MmFWSklHYWoKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          name: envoy-gateway/tls-secret-1
          privateKey: '[redacted]'
        maxVersion: "1.3"
        minVersion: "1.2"
        requireClientCertificate: true
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
  envoy-gateway/gateway-4:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      headers:
        withUnderscoresAction: RejectRequest
        xForwardedClientCert:
          mode: SanitizeSet
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-4
        namespace: envoy-gateway
        sectionName: http-1
      name: envoy-gateway/gateway-4/http-1
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10443
      tls:
        alpnProtocols: null
        caCertificate:
          certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRekNDQWl1Z0F3SUJBZ0lCQVRBTkJna3Foa2lHOXcwQkFRc0ZBREJDTVJNd0VRWURWUVFLRXdwRmJuWnYKZVZCeWIzaDVNUkF3RGdZRFZRUUxFd2RIWVhSbGQyRjVNUmt3RndZRFZRUURFeEJGYm5admVTQkhZWFJsZDJGNQpJRU5CTUNBWERUSTBNRE14TURFMU16SXhOMW9ZRHpJeE1qUXdNekV3TVRZek1qRTNXakJDTVJNd0VRWURWUVFLCkV3cEZiblp2ZVZCeWIzaDVNUkF3RGdZRFZRUUxFd2RIWVhSbGQyRjVNUmt3RndZRFZRUURFeEJGYm5admVTQkgKWVhSbGQyRjVJRU5CTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUE3WkZtR0I0ZQptMUtkR0VvaEFaQmZxeWRBRUdMREhKMVl5ZkhXZGQrdkJBZXZkVzY0Ylp4M3BnZ0pPdGdDbmVQdUZkMDJyRFFTCmRsc0psWC82bUZ0b1FpbG82d3Z4RFNKUmZhVERidGZUancrN2s4eWZkL0pzbWgwUldHK1VleUk3TmE5c1hBejcKYjU3bXB4c0NvTm93emVLNUVUaU9HR05XUGNqRU5Ka1NuQmFyejVtdU4wMHhJWldCVSt5TjVQTEpOeFp2eHBaSgpPbC9TU0k4c25vMGUwUHhBbXAzZmU3UWFYaVpqL1RBR0pQR3VUSmtVeHJIcXlaR0p0WVV4c1M4QTBkVDF6QmpqCml6QTVEcCtiNXl6WW8yM0hoN0JncGJaN1g0Z3NEVGhGdXdDRDZmSHllcHV2MnpIUHF2U3NkcWcyaEFoRHA5MVIKenJuN2E5R3hHMlZTSXdJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBUVl3RHdZRFZSMFRBUUgvQkFVdwpBd0VCL3pBZEJnTlZIUTRFRmdRVVVwUDFhWjFNMktJdVBQV3JOUERWMmM1Q25nb3dEUVlKS29aSWh2Y05BUUVMCkJRQURnZ0VCQUdTRWtBVnorWjBxUzRGbUEwcTRTQ3BJSXE2NGJzZEVqaVV6ZXY3cEsxTEVLMC9ZMjhRQlBpeFYKY1VYZmF4MThWUFI5cGxzMUpnWHRvOXFZK0MwaG5SWmljNjYxMVFUSmxXSzFwNmRpblEvZURkWUNCQytudjV4eApzc0FTd21wbEl4TXZqM1MxcUY2ZHI3c01JMlpWRDVIRWxUV2RPMTlVQkx5aGlLS1pXMkt4RHNZais1TlJ3R0ZlCkcrSnVEZ3E3bmpVTThtZHlZazBOZWhlZmRCVUVVVUNRdG53VXRXOTUvNDI5WHdxUVJPdVJEdGVHVDlrakQrWTUKZWE1bVc0bWZxTGV1R0pYWnM5YmRXaktLZExRUHJuOUlzaFB5c1dxejJIejhkUTFmN045L2c4VVdWU2pkNGN5eApTNUVBb2x6VnYweUI3d0hDV0NnZkcvY2tkT1RVTm5FPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          name: envoy-gateway/target-gateway-4/ca.crt
        certificates:
        - certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUR6akNDQXJhZ0F3SUJBZ0lVT0dKOUx1VGtKWkU0NmNVaUpGYmJ2bm10elFvd0RRWUpLb1pJaHZjTkFRRUwKQlFBd2J6RUxNQWtHQTFVRUJoTUNWVk14Q3pBSkJnTlZCQWdNQWxaQk1SRXdEd1lEVlFRSERBaFRiMjFsUTJsMAplVEVUTUJFR0ExVUVDZ3dLUlc1MmIzbFFjbTk0ZVRFUU1BNEdBMVVFQ3d3SFIyRjBaWGRoZVRFWk1CY0dBMVVFCkF3d1FiWFJzY3k1bGVHRnRjR3hsTG1OdmJUQWdGdzB5TkRBM01UWXlNalV4TWpOYUdBOHlNVEkwTURZeU1qSXkKTlRFeU0xb3diekVMTUFrR0ExVUVCaE1DVlZNeEN6QUpCZ05WQkFnTUFsWkJNUkV3RHdZRFZRUUhEQWhUYjIxbApRMmwwZVRFVE1CRUdBMVVFQ2d3S1JXNTJiM2xRY205NGVURVFNQTRHQTFVRUN3d0hSMkYwWlhkaGVURVpNQmNHCkExVUVBd3dRYlhSc2N5NWxlR0Z0Y0d4bExtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0MKQVFvQ2dnRUJBS3kwZnp5NWFaVnRNajAxVWJPRGtsU1IxbTI1Mkt0QTJ2L2tmT05vNTZkNEJQbGdqVXdXUVZNUgpTclUxZzd4T2tWdjZiL0czdG5tQVhwWWY2VlIxODIyak96cCsxQ0c4ZWlGSEpjT2ZxV2lZMjh1NnFSV2VKUFZlCnpYdUFsMmd4cWJpTzZLdDZwbnI0aXFoVGhIK3ZqV3NXTnBDSVhrbDFydVlYbnhWLzRCOENxY1JJeTZHaEp6bloKRjR3NHJBNkRlRlJmZHl0MWd1bWtkN25PVVhYKzRZMzJrd0xGRU8zR3NnUTh1aVpEQmN1UEs5RjRHRDUydDZYTgowT2tlNTU0eEl2VldvZ1M1Vzl4UFIvcU5kQlpIQ1pid05jZzRRTVllbWZva0pkUXo4elVJcnZ6VUltM3ZvOUs3CnE4MmI1eTVFSm4yWEU5OVMycDZUZnJlSG1sUHpKNHNDQXdFQUFhTmdNRjR3Q3dZRFZSMFBCQVFEQWdTd01CTUcKQTFVZEpRUU1NQW9HQ0NzR0FRVUZCd01CTUJzR0ExVWRFUVFVTUJLQ0VHMTBiSE11WlhoaGJYQnNaUzVqYjIwdwpIUVlEVlIwT0JCWUVGRm1FTjBqRVFpckpYeGlLRHFlK2tTMVV3Q2gyTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCCkFRQ0NTVlluRVJPbHJpWDM2M0VtRzd1b091Nm54ajU1eWVmOXRKbnRubFVMVFZsMjlqc205Z3d5VnFUVCtUcXMKdzFPYW01TExmMEpjSWNRdmFUM203b0RpMElDUUo5eTlRQkNwMTh1TDBUeElDaFdVRTVnRUIxM3MyTzEwWWNFMQp1K2ozSzM0MStQNStoaEJpQnJ1d0dncStkVVRGRTZTYVVMY0xMVlB1RjdTeG1KbTRHK0Q0NVlqM1NDVy9aMzU2CkFXZzB4L0prZGFKSVVLVFJaUDVJTEZKQ1lJTUM3QWI1RmdWeGRCVW5kNWxheUZGb2NVMzRQaDlwZUxiYW00alYKdGt0SGNTSFJ6OERNTm1PNHpHTEZYNzlQR0lsaTZzTDU3V0N6bkw5RFFtajRyajFIS2tyeEdnMVExbUcwbDhOTQo5cXQyWEZNNUttWkVOb2E1MmFWSklHYWoKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          name: envoy-gateway/tls-secret-1
          privateKey: '[redacted]'
        maxVersion: "1.3"
        minVersion: "1.2"
        requireClientCertificate: true
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
  envoy-gateway/gateway-5:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      headers:
        withUnderscoresAction: RejectRequest
        xForwardedClientCert:
          mode: AlwaysForwardOnly
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-5
        namespace: envoy-gateway
        sectionName: http-1
      name: envoy-gateway/gateway-5/http-1
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10443
      tls:
        alpnProtocols: null
        caCertificate:
          certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRekNDQWl1Z0F3SUJBZ0lCQVRBTkJna3Foa2lHOXcwQkFRc0ZBREJDTVJNd0VRWURWUVFLRXdwRmJuWnYKZVZCeWIzaDVNUkF3RGdZRFZRUUxFd2RIWVhSbGQyRjVNUmt3RndZRFZRUURFeEJGYm5admVTQkhZWFJsZDJGNQpJRU5CTUNBWERUSTBNRE14TURFMU16SXhOMW9ZRHpJeE1qUXdNekV3TVRZek1qRTNXakJDTVJNd0VRWURWUVFLCkV3cEZiblp2ZVZCeWIzaDVNUkF3RGdZRFZRUUxFd2RIWVhSbGQyRjVNUmt3RndZRFZRUURFeEJGYm5admVTQkgKWVhSbGQyRjVJRU5CTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUE3WkZtR0I0ZQptMUtkR0VvaEFaQmZxeWRBRUdMREhKMVl5ZkhXZGQrdkJBZXZkVzY0Ylp4M3BnZ0pPdGdDbmVQdUZkMDJyRFFTCmRsc0psWC82bUZ0b1FpbG82d3Z4RFNKUmZhVERidGZUancrN2s4eWZkL0pzbWgwUldHK1VleUk3TmE5c1hBejcKYjU3bXB4c0NvTm93emVLNUVUaU9HR05XUGNqRU5Ka1NuQmFyejVtdU4wMHhJWldCVSt5TjVQTEpOeFp2eHBaSgpPbC9TU0k4c25vMGUwUHhBbXAzZmU3UWFYaVpqL1RBR0pQR3VUSmtVeHJIcXlaR0p0WVV4c1M4QTBkVDF6QmpqCml6QTVEcCtiNXl6WW8yM0hoN0JncGJaN1g0Z3NEVGhGdXdDRDZmSHllcHV2MnpIUHF2U3NkcWcyaEFoRHA5MVIKenJuN2E5R3hHMlZTSXdJREFRQUJvMEl3UURBT0JnTlZIUThCQWY4RUJBTUNBUVl3RHdZRFZSMFRBUUgvQkFVdwpBd0VCL3pBZEJnTlZIUTRFRmdRVVVwUDFhWjFNMktJdVBQV3JOUERWMmM1Q25nb3dEUVlKS29aSWh2Y05BUUVMCkJRQURnZ0VCQUdTRWtBVnorWjBxUzRGbUEwcTRTQ3BJSXE2NGJzZEVqaVV6ZXY3cEsxTEVLMC9ZMjhRQlBpeFYKY1VYZmF4MThWUFI5cGxzMUpnWHRvOXFZK0MwaG5SWmljNjYxMVFUSmxXSzFwNmRpblEvZURkWUNCQytudjV4eApzc0FTd21wbEl4TXZqM1MxcUY2ZHI3c01JMlpWRDVIRWxUV2RPMTlVQkx5aGlLS1pXMkt4RHNZais1TlJ3R0ZlCkcrSnVEZ3E3bmpVTThtZHlZazBOZWhlZmRCVUVVVUNRdG53VXRXOTUvNDI5WHdxUVJPdVJEdGVHVDlrakQrWTUKZWE1bVc0bWZxTGV1R0pYWnM5YmRXaktLZExRUHJuOUlzaFB5c1dxejJIejhkUTFmN045L2c4VVdWU2pkNGN5eApTNUVBb2x6VnYweUI3d0hDV0NnZkcvY2tkT1RVTm5FPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
          name: envoy-gateway/target-gateway-5/ca.crt
        certificates:
        - certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUR6akNDQXJhZ0F3SUJBZ0lVT0dKOUx1VGtKWkU0NmNVaUpGYmJ2bm10elFvd0RRWUpLb1pJaHZjTkFRRUwKQlFBd2J6RUxNQWtHQTFVRUJoTUNWVk14Q3pBSkJnTlZCQWdNQWxaQk1SRXdEd1lEVlFRSERBaFRiMjFsUTJsMAplVEVUTUJFR0ExVUVDZ3dLUlc1MmIzbFFjbTk0ZVRFUU1BNEdBMVVFQ3d3SFIyRjBaWGRoZVRFWk1CY0dBMVVFCkF3d1FiWFJzY3k1bGVHRnRjR3hsTG1OdmJUQWdGdzB5TkRBM01UWXlNalV4TWpOYUdBOHlNVEkwTURZeU1qSXkKTlRFeU0xb3diekVMTUFrR0ExVUVCaE1DVlZNeEN6QUpCZ05WQkFnTUFsWkJNUkV3RHdZRFZRUUhEQWhUYjIxbApRMmwwZVRFVE1CRUdBMVVFQ2d3S1JXNTJiM2xRY205NGVURVFNQTRHQTFVRUN3d0hSMkYwWlhkaGVURVpNQmNHCkExVUVBd3dRYlhSc2N5NWxlR0Z0Y0d4bExtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0MKQVFvQ2dnRUJBS3kwZnp5NWFaVnRNajAxVWJPRGtsU1IxbTI1Mkt0QTJ2L2tmT05vNTZkNEJQbGdqVXdXUVZNUgpTclUxZzd4T2tWdjZiL0czdG5tQVhwWWY2VlIxODIyak96cCsxQ0c4ZWlGSEpjT2ZxV2lZMjh1NnFSV2VKUFZlCnpYdUFsMmd4cWJpTzZLdDZwbnI0aXFoVGhIK3ZqV3NXTnBDSVhrbDFydVlYbnhWLzRCOENxY1JJeTZHaEp6bloKRjR3NHJBNkRlRlJmZHl0MWd1bWtkN25PVVhYKzRZMzJrd0xGRU8zR3NnUTh1aVpEQmN1UEs5RjRHRDUydDZYTgowT2tlNTU0eEl2VldvZ1M1Vzl4UFIvcU5kQlpIQ1pid05jZzRRTVllbWZva0pkUXo4elVJcnZ6VUltM3ZvOUs3CnE4MmI1eTVFSm4yWEU5OVMycDZUZnJlSG1sUHpKNHNDQXdFQUFhTmdNRjR3Q3dZRFZSMFBCQVFEQWdTd01CTUcKQTFVZEpRUU1NQW9HQ0NzR0FRVUZCd01CTUJzR0ExVWRFUVFVTUJLQ0VHMTBiSE11WlhoaGJYQnNaUzVqYjIwdwpIUVlEVlIwT0JCWUVGRm1FTjBqRVFpckpYeGlLRHFlK2tTMVV3Q2gyTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCCkFRQ0NTVlluRVJPbHJpWDM2M0VtRzd1b091Nm54ajU1eWVmOXRKbnRubFVMVFZsMjlqc205Z3d5VnFUVCtUcXMKdzFPYW01TExmMEpjSWNRdmFUM203b0RpMElDUUo5eTlRQkNwMTh1TDBUeElDaFdVRTVnRUIxM3MyTzEwWWNFMQp1K2ozSzM0MStQNStoaEJpQnJ1d0dncStkVVRGRTZTYVVMY0xMVlB1RjdTeG1KbTRHK0Q0NVlqM1NDVy9aMzU2CkFXZzB4L0prZGFKSVVLVFJaUDVJTEZKQ1lJTUM3QWI1RmdWeGRCVW5kNWxheUZGb2NVMzRQaDlwZUxiYW00alYKdGt0SGNTSFJ6OERNTm1PNHpHTEZYNzlQR0lsaTZzTDU3V0N6bkw5RFFtajRyajFIS2tyeEdnMVExbUcwbDhOTQo5cXQyWEZNNUttWkVOb2E1MmFWSklHYWoKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
          name: envoy-gateway/tls-secret-1
          privateKey: '[redacted]'
        maxVersion: "1.3"
        minVersion: "1.2"
        requireClientCertificate: true
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
