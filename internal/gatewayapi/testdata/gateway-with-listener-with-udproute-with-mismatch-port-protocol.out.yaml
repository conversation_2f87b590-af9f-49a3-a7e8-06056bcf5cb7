gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: udp
      port: 162
      protocol: UDP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: udp
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: UDPRoute
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/udp
        ports:
        - containerPort: 10162
          name: udp-162
          protocol: UDP
          servicePort: 162
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
udpRoutes:
- apiVersion: gateway.networking.k8s.io/v1alpha2
  kind: UDPRoute
  metadata:
    creationTimestamp: null
    name: udproute-1
    namespace: default
  spec:
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: UDP Port 8080 not found on Service default/service-1
        reason: PortNotFound
        status: "False"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
    udp:
    - address: 0.0.0.0
      name: envoy-gateway/gateway-1/udp
      port: 10162
      route:
        destination:
          metadata:
            kind: UDPRoute
            name: udproute-1
            namespace: default
          name: udproute/default/udproute-1/rule/-1
        name: udproute/default/udproute-1
