backendTrafficPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: BackendTrafficPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-route
    namespace: default
  spec:
    connection:
      bufferLimit: 100M
    httpUpgrade:
    - type: spdy/3.1
    mergeType: StrategicMerge
    rateLimit:
      global:
        rules:
        - clientSelectors:
          - headers:
            - name: x-user-id
              type: Exact
              value: one
          limit:
            requests: 5
            unit: Hour
          shared: true
      type: Global
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
    timeout:
      tcp:
        connectTimeout: 10s
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Merged with policy envoy-gateway/policy-for-gateway1
        reason: Merged
        status: "True"
        type: Merged
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-2
        namespace: envoy-gateway
        sectionName: http
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: BackendTrafficPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-gateway1
    namespace: envoy-gateway
  spec:
    httpUpgrade:
    - type: websocket
    rateLimit:
      global:
        rules:
        - clientSelectors:
          - headers:
            - name: x-user-id
              type: Exact
              value: two
          limit:
            requests: 5
            unit: Hour
          shared: true
      type: Global
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
    timeout:
      http:
        connectionIdleTimeout: 16s
        maxConnectionDuration: 17s
      tcp:
        connectTimeout: 15s
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: 'This policy is being merged by other backendTrafficPolicies for
          these routes: [default/httproute-1]'
        reason: Merged
        status: "True"
        type: Merged
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-2
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: default
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    - name: gateway-2
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-2
        namespace: envoy-gateway
        sectionName: http
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
  envoy-gateway/gateway-2:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-2/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-2
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
      name: envoy-gateway/gateway-2
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    globalResources:
      envoyClientCertificate:
        certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUREVENDQWZXZ0F3SUJBZ0lVRUZNaFA5ZUo5WEFCV3NRNVptNmJSazJjTE5Rd0RRWUpLb1pJaHZjTkFRRUwKQlFBd0ZqRVVNQklHQTFVRUF3d0xabTl2TG1KaGNpNWpiMjB3SGhjTk1qUXdNakk1TURrek1ERXdXaGNOTXpRdwpNakkyTURrek1ERXdXakFXTVJRd0VnWURWUVFEREF0bWIyOHVZbUZ5TG1OdmJUQ0NBU0l3RFFZSktvWklodmNOCkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFKbEk2WXhFOVprQ1BzNnBDUXhickNtZWl4OVA1RGZ4OVJ1NUxENFQKSm1kVzdJS2R0UVYvd2ZMbXRzdTc2QithVGRDaldlMEJUZmVPT1JCYlIzY1BBRzZFbFFMaWNsUVVydW4zcStncwpKcEsrSTdjSStqNXc4STY4WEg1V1E3clZVdGJ3SHBxYncrY1ZuQnFJVU9MaUlhdGpJZjdLWDUxTTF1RjljZkVICkU0RG5jSDZyYnI1OS9SRlpCc2toeHM1T3p3Sklmb2hreXZGd2V1VHd4Sy9WcGpJKzdPYzQ4QUJDWHBOTzlEL3EKRWgrck9hdWpBTWNYZ0hRSVRrQ2lpVVRjVW82TFNIOXZMWlB0YXFmem9acTZuaE1xcFc2NUUxcEF3RjNqeVRUeAphNUk4SmNmU0Zqa2llWjIwTFVRTW43TThVNHhIamFvL2d2SDBDQWZkQjdSTFUyc0NBd0VBQWFOVE1GRXdIUVlEClZSME9CQllFRk9SQ0U4dS8xRERXN2loWnA3Y3g5dFNtUG02T01COEdBMVVkSXdRWU1CYUFGT1JDRTh1LzFERFcKN2loWnA3Y3g5dFNtUG02T01BOEdBMVVkRXdFQi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQgpBRnQ1M3pqc3FUYUg1YThFMmNodm1XQWdDcnhSSzhiVkxNeGl3TkdqYm1FUFJ6K3c2TngrazBBOEtFY0lEc0tjClNYY2k1OHU0b1didFZKQmx6YS9adWpIUjZQMUJuT3BsK2FveTc4NGJiZDRQMzl3VExvWGZNZmJCQ20xdmV2aDkKQUpLbncyWnRxcjRta2JMY3hFcWxxM3NCTEZBUzlzUUxuS05DZTJjR0xkVHAyYm9HK3FjZ3lRZ0NJTTZmOEVNdgpXUGlmQ01NR3V6Sy9HUkY0YlBPL1lGNDhld0R1M1VlaWgwWFhkVUFPRTlDdFVhOE5JaGMxVVBhT3pQcnRZVnFyClpPR2t2L0t1K0I3OGg4U0VzTzlYclFjdXdiT25KeDZLdFIrYWV5a3ZBcFhDUTNmWkMvYllLQUFSK1A4QUpvUVoKYndJVW1YaTRnajVtK2JLUGhlK2lyK0U9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0=
        name: envoy-gateway-system/envoy
        privateKey: '[redacted]'
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: gateway.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/gateway_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /
        traffic:
          backendConnection:
            bufferLimit: 100000000
          httpUpgrade:
          - spdy/3.1
          - websocket
          rateLimit:
            global:
              rules:
              - headerMatches:
                - distinct: false
                  exact: one
                  name: x-user-id
                limit:
                  requests: 5
                  unit: Hour
                name: default/policy-for-route/rule/0
                shared: true
              - headerMatches:
                - distinct: false
                  exact: two
                  name: x-user-id
                limit:
                  requests: 5
                  unit: Hour
                name: envoy-gateway/policy-for-gateway1/rule/0
                shared: true
          timeout:
            http:
              connectionIdleTimeout: 16s
              maxConnectionDuration: 17s
            tcp:
              connectTimeout: 10s
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
  envoy-gateway/gateway-2:
    accessLog:
      json:
      - path: /dev/stdout
    globalResources:
      envoyClientCertificate:
        certificate: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUREVENDQWZXZ0F3SUJBZ0lVRUZNaFA5ZUo5WEFCV3NRNVptNmJSazJjTE5Rd0RRWUpLb1pJaHZjTkFRRUwKQlFBd0ZqRVVNQklHQTFVRUF3d0xabTl2TG1KaGNpNWpiMjB3SGhjTk1qUXdNakk1TURrek1ERXdXaGNOTXpRdwpNakkyTURrek1ERXdXakFXTVJRd0VnWURWUVFEREF0bWIyOHVZbUZ5TG1OdmJUQ0NBU0l3RFFZSktvWklodmNOCkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFKbEk2WXhFOVprQ1BzNnBDUXhickNtZWl4OVA1RGZ4OVJ1NUxENFQKSm1kVzdJS2R0UVYvd2ZMbXRzdTc2QithVGRDaldlMEJUZmVPT1JCYlIzY1BBRzZFbFFMaWNsUVVydW4zcStncwpKcEsrSTdjSStqNXc4STY4WEg1V1E3clZVdGJ3SHBxYncrY1ZuQnFJVU9MaUlhdGpJZjdLWDUxTTF1RjljZkVICkU0RG5jSDZyYnI1OS9SRlpCc2toeHM1T3p3Sklmb2hreXZGd2V1VHd4Sy9WcGpJKzdPYzQ4QUJDWHBOTzlEL3EKRWgrck9hdWpBTWNYZ0hRSVRrQ2lpVVRjVW82TFNIOXZMWlB0YXFmem9acTZuaE1xcFc2NUUxcEF3RjNqeVRUeAphNUk4SmNmU0Zqa2llWjIwTFVRTW43TThVNHhIamFvL2d2SDBDQWZkQjdSTFUyc0NBd0VBQWFOVE1GRXdIUVlEClZSME9CQllFRk9SQ0U4dS8xRERXN2loWnA3Y3g5dFNtUG02T01COEdBMVVkSXdRWU1CYUFGT1JDRTh1LzFERFcKN2loWnA3Y3g5dFNtUG02T01BOEdBMVVkRXdFQi93UUZNQU1CQWY4d0RRWUpLb1pJaHZjTkFRRUxCUUFEZ2dFQgpBRnQ1M3pqc3FUYUg1YThFMmNodm1XQWdDcnhSSzhiVkxNeGl3TkdqYm1FUFJ6K3c2TngrazBBOEtFY0lEc0tjClNYY2k1OHU0b1didFZKQmx6YS9adWpIUjZQMUJuT3BsK2FveTc4NGJiZDRQMzl3VExvWGZNZmJCQ20xdmV2aDkKQUpLbncyWnRxcjRta2JMY3hFcWxxM3NCTEZBUzlzUUxuS05DZTJjR0xkVHAyYm9HK3FjZ3lRZ0NJTTZmOEVNdgpXUGlmQ01NR3V6Sy9HUkY0YlBPL1lGNDhld0R1M1VlaWgwWFhkVUFPRTlDdFVhOE5JaGMxVVBhT3pQcnRZVnFyClpPR2t2L0t1K0I3OGg4U0VzTzlYclFjdXdiT25KeDZLdFIrYWV5a3ZBcFhDUTNmWkMvYllLQUFSK1A4QUpvUVoKYndJVW1YaTRnajVtK2JLUGhlK2lyK0U9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0=
        name: envoy-gateway-system/envoy
        privateKey: '[redacted]'
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-2
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-2/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: gateway.envoyproxy.io
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/gateway_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /
        traffic:
          backendConnection:
            bufferLimit: 100000000
          httpUpgrade:
          - spdy/3.1
          rateLimit:
            global:
              rules:
              - headerMatches:
                - distinct: false
                  exact: one
                  name: x-user-id
                limit:
                  requests: 5
                  unit: Hour
                name: default/policy-for-route/rule/0
                shared: true
          timeout:
            tcp:
              connectTimeout: 10s
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
