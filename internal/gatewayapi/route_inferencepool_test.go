// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package gatewayapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"

	"github.com/envoyproxy/gateway/internal/gatewayapi/resource"
	"github.com/envoyproxy/gateway/internal/ir"
)

func TestProcessInferencePoolDestinationSetting(t *testing.T) {
	translator := &Translator{}

	// Create a mock InferencePool
	inferencePool := &resource.InferencePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-inference-pool",
			Namespace: "default",
		},
		Spec: resource.InferencePoolSpec{
			Selector:         map[string]string{"app": "model-server"},
			TargetPortNumber: 8080,
			EndpointPickerConfig: resource.EndpointPickerConfig{
				ExtensionRef: &resource.Extension{
					ExtensionReference: resource.ExtensionReference{
						Kind: KindPtr(resource.KindService),
						Name: "endpoint-picker-service",
					},
				},
			},
		},
	}

	resources := &resource.Resources{
		InferencePools: []*resource.InferencePool{inferencePool},
	}

	tests := []struct {
		name       string
		backendRef gwapiv1.BackendObjectReference
		expected   *ir.DestinationSetting
	}{
		{
			name: "valid InferencePool destination",
			backendRef: gwapiv1.BackendObjectReference{
				Name: "test-inference-pool",
			},
			expected: &ir.DestinationSetting{
				Name:                  "test-destination",
				Protocol:              ir.HTTP,
				Weight:                uint32Ptr(1),
				IsOriginalDestination: true,
				InferencePoolRef: &ir.InferencePoolReference{
					Name:      "test-inference-pool",
					Namespace: "default",
					Port:      8080,
				},
			},
		},
		{
			name: "InferencePool not found",
			backendRef: gwapiv1.BackendObjectReference{
				Name: "nonexistent-inference-pool",
			},
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := translator.processInferencePoolDestinationSetting(
				"test-destination",
				tt.backendRef,
				"default",
				ir.HTTP,
				resources,
			)

			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				require.NotNil(t, result)
				assert.Equal(t, tt.expected.Name, result.Name)
				assert.Equal(t, tt.expected.Protocol, result.Protocol)
				assert.Equal(t, *tt.expected.Weight, *result.Weight)
				assert.Equal(t, tt.expected.IsOriginalDestination, result.IsOriginalDestination)
				require.NotNil(t, result.InferencePoolRef)
				assert.Equal(t, tt.expected.InferencePoolRef.Name, result.InferencePoolRef.Name)
				assert.Equal(t, tt.expected.InferencePoolRef.Namespace, result.InferencePoolRef.Namespace)
				assert.Equal(t, tt.expected.InferencePoolRef.Port, result.InferencePoolRef.Port)
			}
		})
	}
}

func TestProcessDestination_InferencePool(t *testing.T) {
	translator := &Translator{}

	// Create a mock InferencePool
	inferencePool := &resource.InferencePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-inference-pool",
			Namespace: "default",
		},
		Spec: resource.InferencePoolSpec{
			Selector:         map[string]string{"app": "model-server"},
			TargetPortNumber: 8080,
			EndpointPickerConfig: resource.EndpointPickerConfig{
				ExtensionRef: &resource.Extension{
					ExtensionReference: resource.ExtensionReference{
						Kind: KindPtr(resource.KindService),
						Name: "endpoint-picker-service",
					},
				},
			},
		},
	}

	resources := &resource.Resources{
		InferencePools: []*resource.InferencePool{inferencePool},
	}

	// Create a mock HTTPRoute context
	parentReference := gwapiv1.ParentReference{
		Name: "test-gateway",
	}
	httpRoute := &HTTPRouteContext{
		HTTPRoute: &gwapiv1.HTTPRoute{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "test-route",
				Namespace: "default",
			},
			Spec: gwapiv1.HTTPRouteSpec{
				CommonRouteSpec: gwapiv1.CommonRouteSpec{
					ParentRefs: []gwapiv1.ParentReference{parentReference},
				},
			},
			Status: gwapiv1.HTTPRouteStatus{
				RouteStatus: gwapiv1.RouteStatus{
					Parents: []gwapiv1.RouteParentStatus{
						{
							ParentRef: parentReference,
						},
					},
				},
			},
		},
	}

	// Create a mock parent context
	parentRef := &RouteParentContext{
		HTTPRoute:       httpRoute.HTTPRoute,
		ParentReference: &parentReference,
	}

	// Create a mock backend reference context
	backendRefContext := gwapiv1.HTTPBackendRef{
		BackendRef: gwapiv1.BackendRef{
			BackendObjectReference: gwapiv1.BackendObjectReference{
				Kind: KindPtr(resource.KindInferencePool),
				Name: "test-inference-pool",
			},
		},
	}

	result, err := translator.processDestination(
		"test-destination",
		backendRefContext,
		parentRef,
		httpRoute,
		resources,
	)

	assert.NoError(t, err)
	require.NotNil(t, result)
	assert.Equal(t, "test-destination", result.Name)
	assert.Equal(t, ir.HTTP, result.Protocol)
	assert.Equal(t, uint32(1), *result.Weight)
	assert.True(t, result.IsOriginalDestination)
	require.NotNil(t, result.InferencePoolRef)
	assert.Equal(t, "test-inference-pool", result.InferencePoolRef.Name)
	assert.Equal(t, "default", result.InferencePoolRef.Namespace)
	assert.Equal(t, int32(8080), result.InferencePoolRef.Port)
}

// Helper function
func uint32Ptr(val uint32) *uint32 {
	return &val
}
