// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package gatewayapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	inferencev1a2 "sigs.k8s.io/gateway-api-inference-extension/api/v1alpha2"
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"

	"github.com/envoyproxy/gateway/internal/gatewayapi/resource"
	"github.com/envoyproxy/gateway/internal/ir"
)

func TestProcessInferencePoolDestinationSetting(t *testing.T) {
	translator := &Translator{}

	// Create a mock InferencePool using the official API
	inferencePool := &inferencev1a2.InferencePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-inference-pool",
			Namespace: "default",
		},
		Spec: inferencev1a2.InferencePoolSpec{
			Selector:         map[inferencev1a2.LabelKey]inferencev1a2.LabelValue{"app": "model-server"},
			TargetPortNumber: 8080,
			EndpointPickerConfig: inferencev1a2.EndpointPickerConfig{
				ExtensionRef: &inferencev1a2.Extension{
					ExtensionReference: inferencev1a2.ExtensionReference{
						Kind: (*inferencev1a2.Kind)(KindPtr(resource.KindService)),
						Name: "endpoint-picker-service",
					},
				},
			},
		},
	}

	resources := &resource.Resources{
		InferencePools: []*inferencev1a2.InferencePool{inferencePool},
	}

	tests := []struct {
		name       string
		backendRef gwapiv1.BackendObjectReference
		expected   *ir.DestinationSetting
	}{
		{
			name: "valid InferencePool destination",
			backendRef: gwapiv1.BackendObjectReference{
				Name: "test-inference-pool",
			},
			expected: &ir.DestinationSetting{
				Name:                  "test-destination",
				Protocol:              ir.HTTP,
				Weight:                uint32Ptr(1),
				IsOriginalDestination: true,
				InferencePoolRef: &ir.InferencePoolReference{
					Name:      "test-inference-pool",
					Namespace: "default",
					Port:      8080,
				},
			},
		},
		{
			name: "InferencePool not found",
			backendRef: gwapiv1.BackendObjectReference{
				Name: "nonexistent-inference-pool",
			},
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := translator.processInferencePoolDestinationSetting(
				"test-destination",
				tt.backendRef,
				"default",
				ir.HTTP,
				resources,
			)

			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				require.NotNil(t, result)
				assert.Equal(t, tt.expected.Name, result.Name)
				assert.Equal(t, tt.expected.Protocol, result.Protocol)
				assert.Equal(t, *tt.expected.Weight, *result.Weight)
				assert.Equal(t, tt.expected.IsOriginalDestination, result.IsOriginalDestination)
				require.NotNil(t, result.InferencePoolRef)
				assert.Equal(t, tt.expected.InferencePoolRef.Name, result.InferencePoolRef.Name)
				assert.Equal(t, tt.expected.InferencePoolRef.Namespace, result.InferencePoolRef.Namespace)
				assert.Equal(t, tt.expected.InferencePoolRef.Port, result.InferencePoolRef.Port)
			}
		})
	}
}

// TestProcessDestination_InferencePool is commented out due to complexity in setting up the full context
// The core functionality is tested in TestProcessInferencePoolDestinationSetting
/*
func TestProcessDestination_InferencePool(t *testing.T) {
	// This test is complex due to the reflection-based context setup
	// The core InferencePool processing logic is tested in TestProcessInferencePoolDestinationSetting
}
*/

// Helper function
func uint32Ptr(val uint32) *uint32 {
	return &val
}
