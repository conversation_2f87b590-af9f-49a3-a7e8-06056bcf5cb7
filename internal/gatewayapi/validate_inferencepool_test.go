// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package gatewayapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"
	gwapiv1a2 "sigs.k8s.io/gateway-api/apis/v1alpha2"

	"github.com/envoyproxy/gateway/internal/gatewayapi/resource"
)

func TestValidateBackendRefKind_InferencePool(t *testing.T) {
	translator := &Translator{}

	tests := []struct {
		name        string
		backendRef  *gwapiv1a2.BackendRef
		expectError bool
	}{
		{
			name: "valid InferencePool backend",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Kind: KindPtr(resource.KindInferencePool),
					Name: "test-inference-pool",
				},
			},
			expectError: false,
		},
		{
			name: "valid Service backend",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Kind: KindPtr(resource.KindService),
					Name: "test-service",
				},
			},
			expectError: false,
		},
		{
			name: "invalid backend kind",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Kind: KindPtr("InvalidKind"),
					Name: "test-backend",
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := translator.validateBackendRefKind(tt.backendRef)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateBackendRefGroup_InferencePool(t *testing.T) {
	translator := &Translator{}

	tests := []struct {
		name        string
		backendRef  *gwapiv1a2.BackendRef
		expectError bool
	}{
		{
			name: "valid inference group",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Group: GroupPtr("inference.gateway.networking.k8s.io"),
					Kind:  KindPtr(resource.KindInferencePool),
					Name:  "test-inference-pool",
				},
			},
			expectError: false,
		},
		{
			name: "valid core group (empty)",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Kind: KindPtr(resource.KindService),
					Name: "test-service",
				},
			},
			expectError: false,
		},
		{
			name: "invalid group",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Group: GroupPtr("invalid.group"),
					Kind:  KindPtr(resource.KindInferencePool),
					Name:  "test-inference-pool",
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := translator.validateBackendRefGroup(tt.backendRef)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateBackendPort_InferencePool(t *testing.T) {
	translator := &Translator{}

	tests := []struct {
		name        string
		backendRef  *gwapiv1a2.BackendRef
		expectError bool
	}{
		{
			name: "InferencePool without port (valid)",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Kind: KindPtr(resource.KindInferencePool),
					Name: "test-inference-pool",
				},
			},
			expectError: false,
		},
		{
			name: "Service without port (invalid)",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Kind: KindPtr(resource.KindService),
					Name: "test-service",
				},
			},
			expectError: true,
		},
		{
			name: "Service with port (valid)",
			backendRef: &gwapiv1a2.BackendRef{
				BackendObjectReference: gwapiv1.BackendObjectReference{
					Kind: KindPtr(resource.KindService),
					Name: "test-service",
					Port: PortPtr(80),
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := translator.validateBackendPort(tt.backendRef)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateBackendRefInferencePool(t *testing.T) {
	translator := &Translator{}

	// Create a mock InferencePool
	inferencePool := &resource.InferencePool{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-inference-pool",
			Namespace: "default",
		},
		Spec: resource.InferencePoolSpec{
			Selector:         map[string]string{"app": "model-server"},
			TargetPortNumber: 8080,
			EndpointPickerConfig: resource.EndpointPickerConfig{
				ExtensionRef: &resource.Extension{
					ExtensionReference: resource.ExtensionReference{
						Kind: KindPtr(resource.KindService),
						Name: "endpoint-picker-service",
					},
				},
			},
		},
	}

	resources := &resource.Resources{
		InferencePools: []*resource.InferencePool{inferencePool},
	}

	tests := []struct {
		name        string
		backendRef  gwapiv1a2.BackendObjectReference
		expectError bool
	}{
		{
			name: "valid InferencePool reference",
			backendRef: gwapiv1a2.BackendObjectReference{
				Name: "test-inference-pool",
			},
			expectError: false,
		},
		{
			name: "InferencePool not found",
			backendRef: gwapiv1a2.BackendObjectReference{
				Name: "nonexistent-inference-pool",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := translator.validateBackendRefInferencePool(tt.backendRef, resources, "default")
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Helper function
func PortPtr(port int) *gwapiv1.PortNumber {
	p := gwapiv1.PortNumber(port)
	return &p
}
