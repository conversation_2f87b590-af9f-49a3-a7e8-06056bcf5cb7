- loadAssignment:
    clusterName: mock-extension-injected-cluster
    endpoints:
    - lbEndpoints:
      - endpoint:
          address:
            socketAddress:
              address: exampleservice.examplenamespace.svc.cluster.local
              portValue: 5000
  name: mock-extension-injected-cluster
  transportSocket:
    name: envoy.transport_sockets.tls
    typedConfig:
      '@type': type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
      commonTlsContext:
        tlsCertificateSdsSecretConfigs:
        - name: default
          sdsConfig:
            apiConfigSource:
              apiType: GRPC
              grpcServices:
              - envoyGrpc:
                  clusterName: sds-cluster
