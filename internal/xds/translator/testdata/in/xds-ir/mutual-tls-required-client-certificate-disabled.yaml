http:
- name: "first-listener"
  address: "::"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  tls:
    alpnProtocols:
    - h2
    - http/1.1
    certificates:
    - name: secret-1
      # byte slice representation of "key-data"
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      # byte slice representation of "key-data"
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    - name: secret-2
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    caCertificate:
      name: ca-cert
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
    requireClientCertificate: false
  routes:
  - name: "first-route"
    hostname: "*"
    destination:
      name: "first-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "first-route-dest/backend/0"
tcp:
- name: "second-listener"
  address: "::"
  port: 10081
  tls:
    certificates:
    - name: secret-3
      # byte slice representation of "key-data"
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      # byte slice representation of "key-data"
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    caCertificate:
      name: ca-cert-2
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
    requireClientCertificate: false
  routes:
  - name: "tls-route-terminate"
    tls:
      terminate:
        alpnProtocols: []
        certificates:
        - name: secret-3
          # byte slice representation of "key-data"
          certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
          # byte slice representation of "key-data"
          privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
        caCertificate:
          name: ca-cert-2
          certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
        requireClientCertificate: false
    destination:
      name: "tls-terminate-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        - host: "*******"
          port: 50001
        name: "tls-terminate-dest/backend/0"
