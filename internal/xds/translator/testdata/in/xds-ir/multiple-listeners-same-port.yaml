http:
- name: "first-listener"
  address: "::"
  port: 10080
  hostnames:
  - "foo.com"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  tls:
    alpnProtocols:
    - h2
    - http/1.1
    certificates:
    - name: first-listener
      # byte slice representation of "cert-data"
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      # byte slice representation of "key-data"
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
  routes:
  - name: "first-route"
    hostname: "*"
    destination:
      name: "first-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "first-route-dest/backend/0"
- name: "second-listener"
  address: "::"
  port: 10080
  hostnames:
  - "foo.net"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  tls:
    alpnProtocols:
    - h2
    - http/1.1
    certificates:
    - name: second-listener
      # byte slice representation of "cert-data"
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      # byte slice representation of "key-data"
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
  routes:
  - name: "second-route"
    hostname: "*"
    destination:
      name: "second-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "second-route-dest/backend/0"
- name: "third-listener"
  address: "::"
  port: 10080
  hostnames:
  - "example.com"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "third-route"
    hostname: "*"
    destination:
      name: "third-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "third-route-dest/backend/0"
- name: "fourth-listener"
  address: "::"
  port: 10080
  hostnames:
  - "example.net"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "fourth-route"
    hostname: "*"
    destination:
      name: "fourth-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "fourth-route-dest/backend/0"
tcp:
- name: "fifth-listener"
  address: "::"
  port: 10080
  routes:
  - name: "fifth-route"
    tls:
      inspector:
        snis:
        - bar.com
    destination:
      name: "tcp-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "tcp-route-dest/backend/0"
- name: "sixth-listener"
  address: "::"
  port: 10080
  routes:
  - name: "sixth-route"
    tls:
      inspector:
        snis:
        - bar.net
    destination:
      name: "tls-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "tls-route-dest/backend/0"
