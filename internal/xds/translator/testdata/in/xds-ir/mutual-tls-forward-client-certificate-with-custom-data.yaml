http:
- name: "first-listener"
  address: "::"
  port: 10001
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  headers:
    xForwardedClientCert:
      mode: Sanitize
  tls:
    alpnProtocols:
    - h2
    - http/1.1
    certificates:
    - name: secret-1
      # byte slice representation of "key-data"
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      # byte slice representation of "key-data"
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    - name: secret-2
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    caCertificate:
      name: ca-cert
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
  routes:
  - name: "first-route"
    hostname: "*"
    destination:
      name: "first-route-dest"
      settings:
      - endpoints:
        - host: "********"
          port: 10001
        name: "first-route-dest/backend/0"
- name: "second-listener"
  address: "::"
  port: 10002
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  headers:
    xForwardedClientCert:
      mode: AppendForward
      certDetailsToAdd: []
  tls:
    alpnProtocols:
    - h2
    - http/1.1
    certificates:
    - name: secret-1
      # byte slice representation of "key-data"
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      # byte slice representation of "key-data"
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    - name: secret-2
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    caCertificate:
      name: ca-cert
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
  routes:
  - name: "second-route"
    hostname: "*"
    destination:
      name: "second-route-dest"
      settings:
      - endpoints:
        - host: "********"
          port: 10002
        name: "second-route-dest/backend/0"
- name: "third-listener"
  address: "::"
  port: 10003
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  headers:
    xForwardedClientCert:
      mode: AppendForward
      certDetailsToAdd:
      - Subject
  tls:
    alpnProtocols:
    - h2
    - http/1.1
    certificates:
    - name: secret-1
      # byte slice representation of "key-data"
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      # byte slice representation of "key-data"
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    - name: secret-2
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    caCertificate:
      name: ca-cert
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
  routes:
  - name: "third-route"
    hostname: "*"
    destination:
      name: "third-route-dest"
      settings:
      - endpoints:
        - host: "********"
          port: 10003
        name: "third-route-dest/backend/0"
- name: "fourth-listener"
  address: "::"
  port: 10004
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  headers:
    xForwardedClientCert:
      mode: SanitizeSet
      certDetailsToAdd:
      - Subject
      - DNS
      - URI
  tls:
    alpnProtocols:
    - h2
    - http/1.1
    certificates:
    - name: secret-1
      # byte slice representation of "key-data"
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      # byte slice representation of "key-data"
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    - name: secret-2
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    caCertificate:
      name: ca-cert
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
  routes:
  - name: "fourth-route"
    hostname: "*"
    destination:
      name: "fourth-route-dest"
      settings:
      - endpoints:
        - host: "********"
          port: 10004
        name: "fourth-route-dest/backend/0"
- name: "fifth-listener"
  address: "::"
  port: 10005
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  headers:
    xForwardedClientCert:
      mode: SanitizeSet
      certDetailsToAdd:
      - Subject
      - DNS
      - Chain
      - Cert
      - URI
  tls:
    alpnProtocols:
    - h2
    - http/1.1
    certificates:
    - name: secret-1
      # byte slice representation of "key-data"
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      # byte slice representation of "key-data"
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    - name: secret-2
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    caCertificate:
      name: ca-cert
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
  routes:
  - name: "fifth-route"
    hostname: "*"
    destination:
      name: "fifth-route-dest"
      settings:
      - endpoints:
        - host: "********"
          port: 10005
        name: "fifth-route-dest/backend/0"
