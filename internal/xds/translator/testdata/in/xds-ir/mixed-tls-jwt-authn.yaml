http:
- name: "first-listener"
  address: "::"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  tls:
    certificates:
    - name: first-listener
      # byte slice representation of "cert-data"
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      # byte slice representation of "key-data"
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
  routes:
  - name: "first-route"
    hostname: "*"
    pathMatch:
      exact: "foo/bar"
    requestAuthentication:
      jwt:
        providers:
        - name: example
          issuer: https://www.example.com
          audiences:
          - foo.com
          remoteJWKS:
            uri: https://localhost/jwt/public-key/jwks.json
    destination:
      name: "first-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "first-route-dest/backend/0"
