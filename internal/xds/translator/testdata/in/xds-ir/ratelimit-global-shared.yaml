globalResources:
  envoyClientCertificate:
    name: envoy-gateway-system/envoy
    privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
metrics:
  enablePerEndpointStats: true
http:
  - name: "first-listener"
    address: "::"
    port: 10080
    hostnames:
      - "*"
    path:
      mergeSlashes: true
      escapedSlashesAction: UnescapeAndRedirect
    routes:
      - name: "first-route"
        hostname: "*"
        traffic:
          name: "test-namespace/test-policy-1"
          rateLimit:
            global:
              rules:
                - name: "test-namespace/test-policy-1/rule/0"
                  headerMatches:
                    - name: "x-user-id"
                      exact: "one"
                  limit:
                    requests: 5
                    unit: second
                  shared: true
        pathMatch:
          exact: "foo/bar"
        destination:
          name: "first-route-dest"
          settings:
            - endpoints:
                - host: "*******"
                  port: 50000
              name: "first-route-dest/backend/0"

      - name: "second-route"
        hostname: "*"
        traffic:
          name: "test-namespace/test-policy-1"
          rateLimit:
            global:
              rules:
                - name: "test-namespace/test-policy-1/rule/0"
                  headerMatches:
                    - name: "x-user-id"
                      exact: "one"
                  limit:
                    requests: 5
                    unit: second
                  shared: true
        pathMatch:
          exact: "foo/baz"
        destination:
          name: "second-route-dest"
          settings:
            - endpoints:
                - host: "*******"
                  port: 50000
              name: "second-route-dest/backend/0"

      - name: "third-route"
        hostname: "*"
        traffic:
          name: "test-namespace/test-policy-2"
          rateLimit:
            global:
              rules:
                - name: "test-namespace/test-policy-2/rule/0"
                  headerMatches:
                    - name: "x-user-id"
                      exact: "two"
                  limit:
                    requests: 10
                    unit: second
                  shared: true
        pathMatch:
          exact: "test"
        destination:
          name: "third-route-dest"
          settings:
            - endpoints:
                - host: "*******"
                  port: 50000
              name: "third-route-dest/backend/0"

      - name: "fourth-route"
        hostname: "*"
        traffic:
          name: "test-namespace/test-policy-3"
          rateLimit:
            global:
              rules:
                - name: "test-namespace/test-policy-3/rule/0"
                  headerMatches:
                    - name: "x-user-id"
                      exact: "two"
                  limit:
                    requests: 10
                    unit: second
                  shared: false
        pathMatch:
          exact: "foo/bar"
        destination:
          name: "fourth-route-dest"
          settings:
            - endpoints:
                - host: "*******"
                  port: 50000
              name: "fourth-route-dest/backend/0"
