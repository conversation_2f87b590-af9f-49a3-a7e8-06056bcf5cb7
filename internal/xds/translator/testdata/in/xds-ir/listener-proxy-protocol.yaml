http:
- name: "first-listener"
  address: "::"
  port: 10080
  hostnames:
  - "foo.com"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  tls:
    alpnProtocols:
    - h2
    - http/1.1
    certificates:
    - name: secret-1
      # byte slice representation of "key-data"
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      # byte slice representation of "key-data"
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    - name: secret-2
      certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
      privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
  enableProxyProtocol: true
  routes:
  - name: "first-route"
    hostname: "*"
    destination:
      name: "first-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "first-route-dest/backend/0"
tcp:
- name: "second-listener"
  address: "::"
  port: 10081
  enableProxyProtocol: true
  routes:
  - name: "tcp-route-dest"
    destination:
      name: "tls-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "tls-route-dest/backend/0"
