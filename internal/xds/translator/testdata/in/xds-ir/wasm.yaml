globalResources:
  envoyClientCertificate:
    name: envoy-gateway-system/envoy
    privateKey: [107, 101, 121, 45, 100, 97, 116, 97]
    certificate: [99, 101, 114, 116, 45, 100, 97, 116, 97]
http:
- address: 0.0.0.0
  hostnames:
  - '*'
  isHTTP2: false
  name: envoy-gateway/gateway-1/http
  path:
    escapedSlashesAction: UnescapeAndRedirect
    mergeSlashes: true
  port: 10080
  routes:
  - destination:
      name: httproute/default/httproute-1/rule/0
      settings:
      - addressType: IP
        endpoints:
        - host: *******
          port: 8080
        protocol: HTTP
        weight: 1
        name: httproute/default/httproute-1/rule/0/backend/0
    hostname: www.example.com
    isHTTP2: false
    name: httproute/default/httproute-1/rule/0/match/0/www_example_com
    pathMatch:
      distinct: false
      name: ""
      prefix: /foo
    envoyExtensions:
      wasms:
      - config:
          parameter1:
            key1: value1
          parameter2:
            key2:
              key3: value3
        failOpen: true
        httpWasmCode:
          servingURL: https://envoy-gateway:18002/fe571e7b1ef5dc626ceb2c2c86782a134a92989a2643485238951696ae4334c3.wasm
          originalDownloadingURL: https://www.test.com/wasm-filter-4.wasm
          sha256: a1f0b78b8c1320690327800e3a5de10e7dbba7b6c752e702193a395a52c727b6
        name: envoyextensionpolicy/default/policy-for-http-route/wasm/0
        wasmName: wasm-filter-4
  - destination:
      name: httproute/default/httproute-2/rule/0
      settings:
      - addressType: IP
        endpoints:
        - host: *******
          port: 8080
        protocol: HTTP
        weight: 1
        name: httproute/default/httproute-2/rule/0/backend/0
    hostname: www.example.com
    isHTTP2: false
    name: httproute/default/httproute-2/rule/0/match/0/www_example_com
    pathMatch:
      distinct: false
      name: ""
      prefix: /bar
    envoyExtensions:
      wasms:
      - config:
          parameter1:
            key1: value1
            key2: value2
          parameter2: value3
        failOpen: false
        httpWasmCode:
          servingURL: https://envoy-gateway.envoy-gateway-system.svc.cluster.local:18002/5c90b9a82642ce00a7753923fabead306b9d9a54a7c0bd2463a1af3efcfb110b.wasm
          originalDownloadingURL: https://www.example.com/wasm-filter-1.wasm
          sha256: 746df05c8f3a0b07a46c0967cfbc5cbe5b9d48d0f79b6177eeedf8be6c8b34b5
        name: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/0
        wasmName: wasm-filter-1
      - config:
          parameter1: value1
          parameter2: value2
        failOpen: false
        httpWasmCode:
          servingURL: https://envoy-gateway.envoy-gateway-system.svc.cluster.local:18002/7abf116e5cd5a20389604a5ba0f3bd04fdf76f92181fe67506b42c2ee596d3fd.wasm
          originalDownloadingURL: oci://www.example.com/wasm-filter-2:v1.0.0
          sha256: 314100af781b98a8ca175d5bf90a8bf76576e20a2f397a88223404edc6ebfd46
        name: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/1
        wasmName: wasm-filter-2
        rootID: my-root-id
      - config: null
        failOpen: false
        httpWasmCode:
          servingURL: https://envoy-gateway.envoy-gateway-system.svc.cluster.local:18002/42d30b4a4cc631415e6e48c02d244700da327201eb273f752cacf745715b31d9.wasm
          originalDownloadingURL: oci://www.example.com:8080/wasm-filter-3:latest
          sha256: 2a19e4f337e5223d7287e7fccd933fb01905deaff804292e5257f8c681b82bee
        name: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/2
        wasmName: envoyextensionpolicy/envoy-gateway/policy-for-gateway/wasm/2
        hostKeys:
        - SOME_KEY
        - ANOTHER_KEY
