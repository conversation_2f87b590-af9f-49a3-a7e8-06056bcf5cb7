apiVersion: v1
kind: ServiceAccount
metadata:
  creationTimestamp: null
  labels:
    app.kubernetes.io/component: proxy
    app.kubernetes.io/managed-by: envoy-gateway
    app.kubernetes.io/name: envoy
    gateway.envoyproxy.io/owning-gateway-name: gateway-1
    gateway.envoyproxy.io/owning-gateway-namespace: namespace-1
    gateway.networking.k8s.io/gateway-name: gateway-1
  name: gateway-1
  namespace: namespace-1
  ownerReferences:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    name: gateway-1
    uid: test-owner-reference-uid-for-gateway
---
apiVersion: v1
kind: ServiceAccount
metadata:
  creationTimestamp: null
  labels:
    app.kubernetes.io/component: proxy
    app.kubernetes.io/managed-by: envoy-gateway
    app.kubernetes.io/name: envoy
    gateway.envoyproxy.io/owning-gateway-name: gateway-2
    gateway.envoyproxy.io/owning-gateway-namespace: namespace-2
    gateway.networking.k8s.io/gateway-name: gateway-2
  name: gateway-2
  namespace: namespace-2
  ownerReferences:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    name: gateway-2
    uid: test-owner-reference-uid-for-gateway
