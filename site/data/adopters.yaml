adopters:
  - name: "<PERSON><PERSON>"
    logo: "/logos/canva.svg"
    url: "https://canva.com/"
    description: "Canva is using Envoy Gateway to route traffic for user uploads, using consistent hash load balancing, and as the gateway for internal systems."
  - name: "AllFactors"
    logo: "/logos/allfactors.svg"
    url: "https://allfactors.com/"
    description: "Routing all customer traffic to our various backends. Every time a new customer signs up we dynamically add a route to a new hostname so Envoy Gateway is deeply integrated with our product."
  - name: "Tetrate"
    logo: "/logos/tetrate.svg"
    url: "https://www.tetrate.io"
    description: "Tetrate provides Enterprise Gateway (TEG) to end users, which includes a 100% upstream distribution of Envoy Gateway, and management to deliver applications securely, authenticate user traffic, protect services with rate limiting and WAF, and integrate with your observability stack to monitor and observe activity."
  - name: "Airspace Link"
    logo: "/logos/airspacelink.svg"
    url: "https://airspacelink.com/"
    description: "Airspace Link is using Envoy Gateway to route all public APIs to Kubernetes clusters, developers are manipulating routes descriptions using agnostic manifest files, which are then automatically provisioned using Envoy Gateway."
  - name: "Teleport"
    logo: "/logos/teleport.svg"
    url: "https://goteleport.com/"
    description: "Teleport is using Envoy Gateway to manage dynamic routing for all traffic to the Teleport Cloud Platform."
  - name: "Tencent Cloud"
    logo: "/logos/tencent-cloud.png"
    url: "https://www.tencentcloud.com"
    description: "Tencent Cloud is using Envoy Gateway as a Kubernetes Cluster Network Addon to manage dynamic routing in the Tencent Kubernetes Engine."
  - name: "QuantCo"
    logo: "/logos/quantco.png"
    url: "https://www.quantco.com"
    description: "QuantCo is using Envoy Gateway to expose various services from our K8s clusters in a secure and flexible way, where developers can deploy and manage their apps and cluster administrators can enforce common security policies like OIDC authentication."
  - name: "Titan"
    logo: "/logos/titan.svg"
    url: "https://www.titan.email"
    description: "Titan uses Envoy Gateway to enhance observability and centralize security for its Kubernetes services, managing critical policies like rate limiting, IP blocking, and access controls—freeing developers from the burden of handling service security."
  - name: "CoactiveAI"
    logo: "/logos/coactive.svg"
    url: "https://www.coactive.ai/"
    description: "CoactiveAI is advancing multimodal content search and analytics at scale, and relies on envoy gateway to simplify self-service routing, consistent API authentication, request modification, safe rollouts with traffic splitting, and request mirroring for data consistency over split deployments."
  - name: "SAP"
    logo: "/logos/sap.svg"
    url: "https://www.sap.com/index.html"
    description: ""
  - name: "Tigera"
    logo: "/logos/tigera.svg"
    url: "https://www.tigera.io/"
    description: ""
  - name: "Kubermatic"
    logo: "/logos/kubermatic.svg"
    url: "https://www.kubermatic.com/"
    description: "Kubermatic is using Envoy Gateway in KubeLB to provide L7 Load Balancing for multiple Kubernetes clusters using GatewayAPI."
  - name: "Rackspace"
    logo: "/logos/rackspace.svg"
    url: "https://www.rackspace.com/"
    description: "Rackspace OpenStack leverages Envoy Gateway within it's openCenter platform to handle dynamic routing across containerized services and virtualized workloads, enhancing traffic management and observability while also consolidating important security policies such as rate limiting, IP blocking, and access control."
  - name: "Docker"
    logo: "/logos/docker.png"
    url: "https://www.docker.com/"
    description: "Docker is using Envoy Gateway to route all internal traffic for Docker Hub."
  - name: "SenseTime"
    logo: "/logos/sensetime.png"
    url: "https://www.sensetime.com/"
    description: "SenseTime is using Envoy Gateway to route most traffic for SenseCore."
  - name: "nemlig"
    logo: "/logos/nemlig.svg"
    url: "https://www.nemlig.com/"
    description: "nemlig.com is using Envoy Gateway to route traffic for internal and externally exposed services, including many of our customer facing APIs. We use Envoy Gateway to enforce security policies, such as JWT authentication as a service on our self-built kubernetes platform."
  - name: "Cortex"
    logo: "/logos/cortex.png"
    url: "https://cortex.io/"
    description: "Cortex is using Envoy Gateway to manage all API traffic as a modern Kubernetes ingress replacement."
  - name: "Bitnami"
    logo: "/logos/bitnami.svg"
    url: "https://bitnami.com/"
    description: "Bitnami has added Envoy Gateway to its application catalog, making it easy for users to deploy and manage the gateway using trusted and up-to-date Helm charts across Kubernetes environments."
  - name: "Alibaba Cloud"
    logo: "/logos/alibaba-cloud.png"
    url: "https://www.alibabacloud.com/"
    description: "Alibaba Cloud Container Service adopts Envoy Gateway as a network addon, providing comprehensive Gateway API support and dedicated LLM load balancing capabilities."
