name: Experimental Conformance Test
on:
  push:
    # Sequence of patterns matched against refs/tags
    tags:
      - "v*.*.*"
    paths:
      - "charts/gateway-helm/crds/gatewayapi-crds.yaml"
      - "test/conformance/experimental_conformance_test.go"
  pull_request:
    paths:
      - "charts/gateway-helm/crds/gatewayapi-crds.yaml"
      - "test/conformance/experimental_conformance_test.go"
  # Add workflow_dispatch to trigger this workflow manually by maintainers.
  workflow_dispatch:

permissions:
  contents: read

jobs:
  experimental-conformance-test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        target:
          - version: v1.30.10
            ipFamily: ipv4
            profile: default
          - version: v1.31.6
            ipFamily: ipv4
            profile: default
          - version: v1.32.3
            # only run ipv6 test on this version to save time
            ipFamily: ipv6
            profile: default
          # TODO: this's IPv4 first, need a way to test IPv6 first.
          - version: v1.33.0
            # only run dual test on latest version to save time
            ipFamily: dual
            profile: default
          - version: v1.33.0
            # only run dual test on latest version to save time
            ipFamily: dual
            profile: gateway-namespace-mode
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
      - uses: ./tools/github-actions/setup-deps

      # gateway api experimental conformance
      - name: Run Experimental Conformance Tests
        env:
          IP_FAMILY: ${{ matrix.target.ipFamily }}
          KUBE_DEPLOY_PROFILE: ${{ matrix.target.profile }}
          CONFORMANCE_REPORT_PATH: conformance-report-k8s-${{ matrix.target.version }}-${{ matrix.target.profile }}.yaml
          KIND_NODE_TAG: ${{ matrix.target.version }}
          IMAGE_PULL_POLICY: IfNotPresent
        run: make experimental-conformance

      - name: Upload Conformance Report
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02  # v4.6.2
        with:
          name: conformance-report-k8s-${{ matrix.target.version }}-${{ matrix.target.profile }}
          path: ./test/conformance/conformance-report-k8s-${{ matrix.target.version }}-${{ matrix.target.profile }}.yaml
