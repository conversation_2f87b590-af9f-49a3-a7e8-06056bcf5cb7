apiVersion: inference.gateway.networking.k8s.io/v1alpha2
kind: InferencePool
metadata:
  name: test-inference-pool
  namespace: gateway-conformance-infra
spec:
  selector:
    app: model-server
  targetPortNumber: 8080
  endpointPickerConfig:
    extensionRef:
      kind: Service
      name: endpoint-picker-service
      portNumber: 9090
---
apiVersion: v1
kind: Service
metadata:
  name: endpoint-picker-service
  namespace: gateway-conformance-infra
spec:
  selector:
    app: endpoint-picker
  ports:
  - port: 9090
    targetPort: 9090
    protocol: TCP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: endpoint-picker
  namespace: gateway-conformance-infra
spec:
  replicas: 1
  selector:
    matchLabels:
      app: endpoint-picker
  template:
    metadata:
      labels:
        app: endpoint-picker
    spec:
      containers:
      - name: endpoint-picker
        image: nginx:latest
        ports:
        - containerPort: 9090
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httproute-to-inferencepool
  namespace: gateway-conformance-infra
spec:
  parentRefs:
  - name: same-namespace
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /inference
    backendRefs:
    - group: inference.gateway.networking.k8s.io
      kind: InferencePool
      name: test-inference-pool
---
apiVersion: v1
kind: Service
metadata:
  name: model-server-service
  namespace: gateway-conformance-infra
spec:
  selector:
    app: model-server
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: model-server
  namespace: gateway-conformance-infra
spec:
  replicas: 2
  selector:
    matchLabels:
      app: model-server
  template:
    metadata:
      labels:
        app: model-server
    spec:
      containers:
      - name: model-server
        image: nginx:latest
        ports:
        - containerPort: 8080
