# This test is to verify that the basic http route works with EG having custom security context user id
# The custom security context user id is set to 65534 in the test go code
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: custom-eg-security-context-userid
  namespace: gateway-conformance-infra
spec:
  parentRefs:
    - name: same-namespace
  rules:
    - backendRefs:
        - name: infra-backend-v1
          port: 8080
