apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: eg-special-case-datadog
  namespace: gateway-conformance-infra
spec:
  gatewayClassName: "{GATEWAY_CLASS_NAME}"
  listeners:
    - name: http
      port: 80
      protocol: HTTP
      allowedRoutes:
        namespaces:
          from: All
  infrastructure:
    parametersRef:
      group: gateway.envoyproxy.io
      kind: EnvoyProxy
      name: datadog-tracing
---
apiVersion: v1
kind: Service
metadata:
  name: datadog-agent
  namespace: monitoring
spec:
  selector:
    app.kubernetes.io/instance: eg-addons
    app.kubernetes.io/name: opentelemetry-collector
    component: standalone-collector
  ports:
    - protocol: TCP
      port: 8126
      targetPort: 8126
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyProxy
metadata:
  name: datadog-tracing
  namespace: gateway-conformance-infra
spec:
  ipFamily: IPv4
  logging:
    level:
      default: debug
  telemetry:
    tracing:
      provider:
        type: Datadog
        backendRefs:
          - name: backend-fqdn
            kind: Backend
            group: gateway.envoyproxy.io
            port: 8126
      customTags:
        "provider":
          type: Literal
          literal:
            value: "datadog"
        "k8s.cluster.name":
          type: Literal
          literal:
            value: "envoy-gateway"
        "k8s.pod.name":
          type: Environment
          environment:
            name: ENVOY_POD_NAME
            defaultValue: "-"
        "k8s.namespace.name":
          type: Environment
          environment:
            name: ENVOY_POD_NAMESPACE
            defaultValue: "envoy-gateway-system"
  shutdown:
    drainTimeout: 5s
    minDrainDuration: 1s
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: backend-fqdn
  namespace: gateway-conformance-infra
spec:
  endpoints:
    - fqdn:
        hostname: datadog-agent.monitoring.svc.cluster.local
        port: 8126
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: tracing-datadog
  namespace: gateway-conformance-infra
spec:
  parentRefs:
    - name: eg-special-case-datadog
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /datadog
      backendRefs:
        - name: infra-backend-v2
          port: 8080
