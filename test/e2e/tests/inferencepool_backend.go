// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

//go:build e2e
// +build e2e

package tests

import (
	"testing"

	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/gateway-api/conformance/utils/http"
	"sigs.k8s.io/gateway-api/conformance/utils/kubernetes"
	"sigs.k8s.io/gateway-api/conformance/utils/suite"
)

func init() {
	ConformanceTests = append(ConformanceTests, InferencePoolBackendTest)
}

var InferencePoolBackendTest = suite.ConformanceTest{
	ShortName:   "InferencePoolBackend",
	Description: "Test that HTT<PERSON>oute can reference InferencePool as a backend",
	Features: []suite.SupportedFeature{
		suite.SupportedFeature("InferencePool"),
	},
	Manifests: []string{"testdata/inferencepool-backend.yaml"},
	Test: func(t *testing.T, suite *suite.ConformanceTestSuite) {
		ns := "gateway-conformance-infra"
		routeNN := types.NamespacedName{Name: "httproute-to-inferencepool", Namespace: ns}
		gwNN := types.NamespacedName{Name: "same-namespace", Namespace: ns}
		gwAddr := kubernetes.GatewayAndHTTPRoutesMustBeAccepted(t, suite.Client, suite.TimeoutConfig, suite.ControllerName, kubernetes.NewGatewayRef(gwNN), routeNN)

		// Test that the route is accepted and configured
		// Note: This test validates that the InferencePool backend reference is accepted
		// The actual routing behavior would depend on the ext-proc implementation
		expectedResponse := http.ExpectedResponse{
			Request: http.Request{
				Path: "/inference",
			},
			Response: http.Response{
				StatusCode: 500, // Expected since we don't have actual ext-proc implementation
			},
			Namespace: ns,
		}

		http.MakeRequestAndExpectEventuallyConsistentResponse(t, suite.RoundTripper, suite.TimeoutConfig, gwAddr, expectedResponse)
	},
}
